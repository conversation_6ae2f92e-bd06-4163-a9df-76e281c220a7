'use client';

import React from 'react';

/**
 * Download button that uses our proxy API to download files directly
 * without exposing <PERSON><PERSON><PERSON> to the user.
 */
export default function DownloadButton({
  expired,
  fileId,
  filename,
}: {
  expired: boolean;
  fileId: string;
  filename: string;
}) {
  const [isDownloading, setIsDownloading] = React.useState(false);

  const handleDownload = React.useCallback(async () => {
    if (expired || isDownloading) return;

    setIsDownloading(true);
    
    try {
      // Use our proxy API with the filename parameter
      const downloadUrl = `/api/download/${fileId}?filename=${encodeURIComponent(filename)}`;
      
      // Create a temporary link element and click it to trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
    } catch (error) {
      console.error('Download failed:', error);
      // Optionally show error to user
    } finally {
      // Reset download state after a short delay
      setTimeout(() => {
        setIsDownloading(false);
      }, 2000);
    }
  }, [expired, isDownloading, fileId, filename]);

  return (
    <button
      type="button"
      onClick={handleDownload}
      className={`inline-flex items-center justify-center rounded-xl px-6 py-3 text-sm font-semibold transition ${
        expired || isDownloading
          ? 'cursor-not-allowed border border-gray-200 bg-gray-100 text-gray-400'
          : 'border border-blue-600 bg-blue-600 text-white hover:bg-blue-700'
      }`}
      disabled={expired || isDownloading}
      aria-disabled={expired || isDownloading}
    >
      {!expired ? (
        <span className="flex items-center gap-2">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" aria-hidden="true">
            <path
              d="M12 3v12m0 0l-4-4m4 4l4-4M4 21h16"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          {isDownloading ? 'Downloading...' : 'Download'}
        </span>
      ) : (
        'Fil udløbet'
      )}
    </button>
  );
}