import { getUploadProgressCollection } from './mongodb'

// MongoDB-based progress store (works across serverless instances)
export async function updateUploadProgress(uploadId: string, progress: number, status: string, error?: string) {
  try {
    console.log(`Progress Store: Setting ${uploadId} to ${progress}% - ${status}`)
    const progressData = {
      uploadId,
      progress,
      status,
      error,
      timestamp: new Date(),
      updatedAt: new Date()
    }
    
    const collection = await getUploadProgressCollection()
    await collection.updateOne(
      { uploadId },
      { $set: progressData },
      { upsert: true }
    )
    console.log(`Progress Store: Updated MongoDB for ${uploadId}`)
  } catch (e) {
    console.error('Failed to write progress to MongoDB:', e)
  }
}

export async function getUploadProgress(uploadId: string) {
  try {
    const collection = await getUploadProgressCollection()
    const progress = await collection.findOne({ uploadId })
    
    if (!progress) {
      console.log(`Progress Store: No record found for ${uploadId}`)
      return null
    }
    
    console.log(`Progress Store: Getting ${uploadId} - found: yes, ${progress.progress}% - ${progress.status}`)
    return {
      progress: progress.progress,
      status: progress.status,
      error: progress.error,
      timestamp: progress.timestamp
    }
  } catch (e) {
    console.error('Failed to read progress from MongoDB:', e)
    return null
  }
}

export async function deleteUploadProgress(uploadId: string) {
  try {
    const collection = await getUploadProgressCollection()
    await collection.deleteOne({ uploadId })
    console.log(`Progress Store: Deleted ${uploadId} from MongoDB`)
  } catch (e) {
    console.error('Failed to delete progress from MongoDB:', e)
  }
}

// Clean up old progress records (older than 1 hour)
export async function cleanupOldProgress() {
  try {
    const collection = await getUploadProgressCollection()
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    await collection.deleteMany({
      updatedAt: { $lt: oneHourAgo }
    })
    console.log('Progress Store: Cleaned up old progress records')
  } catch (e) {
    console.error('Failed to cleanup old progress:', e)
  }
} 