export type LastFmTrack = {
  isPlaying: boolean;
  title: string | null;
  artists: string | null;
  album: string | null;
  image: string | null;
  url: string | null;
  playedAt?: string | null; // ISO timestamp for last played
  durationMs?: number | null;
};

function optionalEnv(name: string): string | null {
  const value = process.env[name];
  return value ?? null;
}

export async function fetchLastFmNowPlaying(): Promise<LastFmTrack> {
  const apiKey = optionalEnv('LASTFM_API_KEY');
  const username = optionalEnv('LASTFM_USERNAME');
  if (!apiKey || !username) {
    return {
      isPlaying: false,
      title: null,
      artists: null,
      album: null,
      image: null,
      url: null,
      playedAt: null,
      durationMs: null,
    };
  }

  const url = new URL('https://ws.audioscrobbler.com/2.0/');
  url.searchParams.set('method', 'user.getrecenttracks');
  url.searchParams.set('user', username);
  url.searchParams.set('api_key', apiKey);
  url.searchParams.set('format', 'json');
  url.searchParams.set('limit', '1');

  const res = await fetch(url.toString(), { cache: 'no-store' });
  if (!res.ok) {
    // Graceful fallback on API errors
    return {
      isPlaying: false,
      title: null,
      artists: null,
      album: null,
      image: null,
      url: null,
      playedAt: null,
    };
  }

  const data = await res.json();
  const track = data?.recenttracks?.track?.[0];

  if (!track) {
    return {
      isPlaying: false,
      title: null,
      artists: null,
      album: null,
      image: null,
      url: null,
      playedAt: null,
    };
  }

  const isNowPlaying = Boolean(track['@attr'] && track['@attr'].nowplaying === 'true');
  const image = Array.isArray(track.image)
    ? (track.image.find((img: any) => img.size === 'large')?.['#text'] || track.image[track.image.length - 1]?.['#text'] || null)
    : null;

  // Try to fetch track duration via track.getInfo
  let durationMs: number | null = null;
  try {
    const infoUrl = new URL('https://ws.audioscrobbler.com/2.0/');
    infoUrl.searchParams.set('method', 'track.getInfo');
    if (track.artist?.['#text']) infoUrl.searchParams.set('artist', track.artist['#text']);
    if (track.name) infoUrl.searchParams.set('track', track.name);
    infoUrl.searchParams.set('api_key', apiKey);
    infoUrl.searchParams.set('format', 'json');

    const infoRes = await fetch(infoUrl.toString(), { cache: 'no-store' });
    if (infoRes.ok) {
      const info = await infoRes.json();
      const durStr = info?.track?.duration; // often milliseconds as string
      const dur = typeof durStr === 'string' ? parseInt(durStr, 10) : Number(durStr);
      if (Number.isFinite(dur) && dur > 0) {
        durationMs = dur;
      }
    }
  } catch (_) {
    // ignore
  }

  return {
    isPlaying: isNowPlaying,
    title: track.name || null,
    artists: track.artist?.['#text'] || null,
    album: track.album?.['#text'] || null,
    image: image && image.length > 0 ? image : null,
    url: track.url || null,
    playedAt: isNowPlaying ? null : (track.date?.uts ? new Date(Number(track.date.uts) * 1000).toISOString() : null),
    durationMs,
  };
}
