export const metadata = {
  title: "Roadmap | FlyFiles",
  description:
    "Se vores roadmap for FlyFiles – hvad arbejder vi på nu, hvad kommer snart, og hvad overvejer vi til fremtiden.",
};

type Status = "nu" | "snart" | "overvej";

type RoadmapItem = {
  title: string;
  description?: string;
  status: Status;
};

const ROADMAP: RoadmapItem[] = [
  {
    title:
      "Forbedret upload-stabilitet ved store filer (chunked upload for finere kontrol)",
    status: "nu",
  },
  {
    title:
      "Bedre fremdrift pr. fil med hastighed og estimeret tid",
    status: "nu",
  },
  {
    title:
      "Optimering af download-oplevelsen for modtagere uden konto",
    status: "nu",
  },
  {
    title: "Brugerprofiler med historik og hurtig deling",
    status: "snart",
  },
  {
    title:
      "Enkel deling med adgangskoder og udløbstid per link",
    status: "snart",
  },
  {
    title:
      "Bedre e-mail-notifikationer ved færdige uploads",
    status: "snart",
  },
  {
    title: "Team-funktioner og delte mapper",
    status: "overvej",
  },
  {
    title:
      "Integrationer til populære værktøjer (f.eks. Slack/Teams)",
    status: "overvej",
  },
  {
    title:
      "Branding af download-sider for partnere/teams",
    status: "overvej",
  },
];

const STATUS_META: Record<
  Status,
  { label: string; color: string; chip: string; dot: string }
> = {
  nu: {
    label: "Arbejder på nu",
    color: "from-emerald-500 to-emerald-600",
    chip: "bg-emerald-100 text-emerald-800 ring-1 ring-inset ring-emerald-200",
    dot: "bg-emerald-500",
  },
  snart: {
    label: "Kommer snart",
    color: "from-blue-500 to-blue-600",
    chip: "bg-blue-100 text-blue-800 ring-1 ring-inset ring-blue-200",
    dot: "bg-blue-500",
  },
  overvej: {
    label: "På tegnebrættet",
    color: "from-slate-400 to-slate-500",
    chip: "bg-slate-100 text-slate-800 ring-1 ring-inset ring-slate-200",
    dot: "bg-slate-400",
  },
};

function StatusBadge({ status }: { status: Status }) {
  const meta = STATUS_META[status];
  return (
    <span
      className={`inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium ${meta.chip}`}
    >
      <span
        className={`h-1.5 w-1.5 rounded-full ${meta.dot}`}
        aria-hidden="true"
      />
      {meta.label}
    </span>
  );
}

function SectionCard({
  status,
  items,
}: {
  status: Status;
  items: RoadmapItem[];
}) {
  const meta = STATUS_META[status];

  return (
    <section
      aria-labelledby={`section-${status}`}
      className="relative overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm"
    >
      <div
        className={`absolute inset-x-0 -top-24 h-44 bg-gradient-to-b ${meta.color} opacity-10 blur-2xl`}
        aria-hidden="true"
      />
      <div className="relative p-6 sm:p-8">
        <div className="flex items-center justify-between">
          <h2
            id={`section-${status}`}
            className="text-lg sm:text-xl font-extrabold tracking-tight text-slate-900"
          >
            {STATUS_META[status].label}
          </h2>
          <StatusBadge status={status} />
        </div>

        <ol className="mt-6 space-y-4">
          {items.map((item, idx) => (
            <li key={idx} className="relative">
              <div className="flex items-start gap-3">
                <div
                  className={`mt-1 h-2 w-2 rounded-full ${meta.dot} ring-4 ring-${meta.dot}/20`}
                  aria-hidden="true"
                />
                <div className="flex-1">
                  <p className="text-slate-800 font-medium leading-relaxed">
                    {item.title}
                  </p>
                  {item.description && (
                    <p className="mt-1 text-sm text-slate-600">
                      {item.description}
                    </p>
                  )}
                </div>
              </div>
            </li>
          ))}
        </ol>
      </div>
    </section>
  );
}

export default function RoadmapPage() {
  const grouped = {
    nu: ROADMAP.filter((i) => i.status === "nu"),
    snart: ROADMAP.filter((i) => i.status === "snart"),
    overvej: ROADMAP.filter((i) => i.status === "overvej"),
  };

  return (
    <main className="min-h-screen bg-white">
      <header className="relative isolate overflow-hidden bg-gradient-to-b from-blue-700 via-blue-800 to-blue-900 text-white">
        <div className="absolute inset-0 opacity-25 pointer-events-none">
          <div className="absolute -top-20 -left-16 h-72 w-72 rounded-full bg-blue-400 blur-3xl" />
          <div className="absolute top-1/3 -right-10 h-72 w-72 rounded-full bg-cyan-400 blur-3xl" />
          <div className="absolute bottom-0 left-1/4 h-56 w-56 rounded-full bg-indigo-500 blur-3xl" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 py-16 sm:py-20 md:py-28">
          <div className="inline-flex items-center gap-2 rounded-full bg-white/10 px-3 py-1.5 text-xs font-medium tracking-wide backdrop-blur-sm ring-1 ring-inset ring-white/20">
            <span className="h-1.5 w-1.5 rounded-full bg-emerald-300" />
            Om os
          </div>
          <h1 className="mt-4 text-3xl sm:text-4xl md:text-5xl font-extrabold tracking-tight">
            FlyFiles <span className="text-blue-200">Roadmap</span>
          </h1>
          <p className="mt-4 text-blue-100 text-lg md:text-xl max-w-3xl">
            Følg med i hvad vi bygger, hvad der er på vej, og hvad vi
            overvejer. Vi prioriterer ud fra brugerfeedback og løbende
            læring.
          </p>
        </div>
      </header>

      <section
        aria-label="Roadmap"
        className="py-12 sm:py-16 bg-gradient-to-b from-white to-blue-50/40"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="grid gap-6 sm:gap-8 md:grid-cols-3">
            <SectionCard status="nu" items={grouped.nu} />
            <SectionCard status="snart" items={grouped.snart} />
            <SectionCard status="overvej" items={grouped.overvej} />
          </div>

          <div className="mt-12">
            <div className="mx-auto max-w-3xl">
              <div className="relative overflow-hidden rounded-2xl border border-blue-200 bg-blue-50/70 p-6 sm:p-8 shadow-sm">
                <div
                  className="absolute left-0 top-0 h-full w-1.5 bg-blue-500"
                  aria-hidden="true"
                />
                <div className="relative pl-2 sm:pl-1">
                  <h2 className="text-xl sm:text-2xl font-extrabold text-blue-900 tracking-tight text-center">
                    Har du forslag?
                  </h2>
                  <p className="mt-2 text-sm sm:text-base text-blue-900/80 text-center">
                    Del dine idéer og behov – roadmap kan ændre sig baseret på
                    feedback. Din input hjælper os med at prioritere rigtigt.
                  </p>
                  <div className="mt-5 flex items-center justify-center gap-3">
                    <a
                      href="/support"
                      className="inline-flex items-center justify-center rounded-xl bg-blue-600 px-4 sm:px-5 py-2.5 text-sm sm:text-base font-semibold text-white shadow-sm hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 transition"
                    >
                      Giv feedback
                    </a>
                    <a
                      href="/support"
                      className="inline-flex items-center justify-center rounded-xl px-4 sm:px-5 py-2.5 text-sm sm:text-base font-semibold text-blue-700 border border-blue-300 bg-white/80 hover:bg-white transition"
                    >
                      Kontakt support
                    </a>
                  </div>
                </div>
              </div>

              <p className="mt-6 text-center text-xs text-slate-500">
                Sidst opdateret: {new Date().toLocaleDateString("da-DK", { year: "numeric", month: "long" })}
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}