import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Allow user usage endpoint for both authenticated users and guests
  if (pathname.startsWith('/api/user/usage')) {
    return NextResponse.next()
  }

  // Protect selected API routes for authenticated users only
  const isProtectedApi =
    (pathname.startsWith('/api/files') ? request.method !== 'GET' : false) ||
    pathname === '/api/upload' ||
    pathname.startsWith('/api/upload-')

  if (isProtectedApi) {
    // Check for NextAuth v5 session tokens - try multiple possible cookie names
    const sessionToken = request.cookies.get('authjs.session-token') ||
      request.cookies.get('__Secure-authjs.session-token') ||
      request.cookies.get('next-auth.session-token') ||
      request.cookies.get('__Secure-next-auth.session-token')

    if (!sessionToken) {
      // Allow guests that have a guest_session_id cookie
      const guestSession = request.cookies.get('guest_session_id')
      if (!guestSession) {
        return NextResponse.redirect(new URL('/login', request.url))
      }
    }
  }

  // Note: We intentionally do NOT protect /dashboard here so guests can access a limited dashboard
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/api/files/:path*',
    // Allow unauthenticated access to /api/user/usage; other /api/user routes are not matched here
    '/api/upload',
    '/api/upload-:path*'
  ]
}