import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getFilesCollection, getUsersCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

// PATCH - Update file properties (currently only download limit)
export async function PATCH(
  request: NextRequest,
  context: any
) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    // In Next.js App Router, context.params is async in dynamic routes.
    // Await it to avoid "params should be awaited" warning/error.
    const { id: fileId } = await (async () => {
      const params = await (context as any).params
      return { id: params?.id as string | undefined }
    })()

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID required' } as ApiResponse,
        { status: 400 }
      )
    }

    // Validate file ID
    if (!ObjectId.isValid(fileId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file ID' } as ApiResponse,
        { status: 400 }
      )
    }

    const body = await request.json()
    const { downloadLimit } = body

    // Validate download limit
    if (downloadLimit !== undefined && (typeof downloadLimit !== 'number' || downloadLimit < -1)) {
      return NextResponse.json(
        { success: false, error: 'Invalid download limit. Must be -1 (unlimited) or 0 or higher.' } as ApiResponse,
        { status: 400 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    const files = await getFilesCollection()
    
    // Find the file and ensure it belongs to the user
    const file = await files.findOne({
      _id: new ObjectId(fileId),
      ownerId: user._id.toString()
    })

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File not found or access denied' } as ApiResponse,
        { status: 404 }
      )
    }

    // Update the download limit
    const updateData: any = {}
    if (downloadLimit !== undefined) {
      updateData.downloadLimit = downloadLimit
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' } as ApiResponse,
        { status: 400 }
      )
    }

    await files.updateOne(
      { _id: new ObjectId(fileId) },
      { $set: updateData }
    )

    return NextResponse.json({
      success: true,
      message: 'File updated successfully',
      data: { fileId, ...updateData }
    } as ApiResponse)

  } catch (error) {
    console.error('Error updating file:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}


// DELETE - Delete a file
export async function DELETE(
  request: NextRequest,
  context: any
) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    // In Next.js App Router, context.params is async in dynamic routes.
    // Await it to avoid "params should be awaited" warning/error.
    const { id: fileId } = await (async () => {
      const params = await (context as any).params
      return { id: params?.id as string | undefined }
    })()

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID required' } as ApiResponse,
        { status: 400 }
      )
    }

    // Validate file ID
    if (!ObjectId.isValid(fileId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file ID' } as ApiResponse,
        { status: 400 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    const files = await getFilesCollection()
    
    // Find the file and ensure it belongs to the user
    const file = await files.findOne({
      _id: new ObjectId(fileId),
      ownerId: user._id.toString()
    })

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File not found or access denied' } as ApiResponse,
        { status: 404 }
      )
    }

    // Soft delete by setting isActive to false
    await files.updateOne(
      { _id: new ObjectId(fileId) },
      { $set: { isActive: false } }
    )

    // Update user usage (subtract the file size)
    const usageField = user.plan === 'free' ? 'usage.monthly' : 'usage.weekly'
    await users.updateOne(
      { _id: user._id },
      { $inc: { [usageField]: -file.size } }
    )

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    } as ApiResponse)

  } catch (error) {
    console.error('Error deleting file:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}