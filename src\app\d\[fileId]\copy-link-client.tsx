'use client'

import React from 'react'

export function CopyLinkButton({ url }: { url: string }) {
  async function handleCopy() {
    try {
      await navigator.clipboard.writeText(url)
      showStatus('Kopieret!', 'text-green-600')
    } catch {
      showStatus('<PERSON><PERSON> ikke kopiere', 'text-red-600')
    }
  }

  function showStatus(message: string, colorClass: string) {
    const el = document.getElementById('copy-status')
    if (!el) return
    el.textContent = message
    el.classList.remove('opacity-0', 'text-green-600', 'text-red-600')
    el.classList.add('opacity-100', colorClass)
    window.setTimeout(() => {
      el.classList.remove('opacity-100', colorClass)
      el.classList.add('opacity-0')
    }, 1600)
  }

  return (
    <>
      <button
        onClick={handleCopy}
        className="inline-flex items-center justify-center rounded-xl border border-gray-200 bg-white px-6 py-3 text-sm font-semibold text-gray-700 hover:bg-gray-50"
        type="button"
      >
        <PERSON><PERSON><PERSON>r side-link
      </button>
      <div id="copy-status" className="text-center text-xs text-green-600 opacity-0 transition-opacity duration-300 mt-1">
        Kopieret!
      </div>
    </>
  )
}