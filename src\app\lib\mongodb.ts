import { MongoClient, Collection } from 'mongodb';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your MongoDB URI to .env');
}

const uri = process.env.MONGODB_URI;
const options = {
  // Disable client-side encryption to avoid Node.js module issues
  autoEncryption: undefined,
  // Disable other features that might require Node.js modules
  monitorCommands: false,
};

let client: MongoClient;
let clientPromise: Promise<MongoClient>;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  let globalWithMongo = global as typeof globalThis & {
    _mongoClientPromise?: Promise<MongoClient>;
  };

  if (!globalWithMongo._mongoClientPromise) {
    client = new MongoClient(uri, options);
    globalWithMongo._mongoClientPromise = client.connect();
  }
  clientPromise = globalWithMongo._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

export { clientPromise };

// Database helper functions
export async function getDatabase() {
  const client = await clientPromise;
  return client.db('flyfiles');
}

// Strongly-typed session documents to satisfy update operators
export interface SessionDoc {
  sessionId: string;
  totalUploaded: number;
  createdAt: Date;
  expiresAt?: Date;
  files: string[];
}

export async function getUsersCollection() {
  const db = await getDatabase();
  return db.collection('users');
}

export async function getFilesCollection() {
  const db = await getDatabase();
  return db.collection('files');
}

export async function getSessionsCollection(): Promise<Collection<SessionDoc>> {
  const db = await getDatabase();
  return db.collection<SessionDoc>('sessions');
}

export async function getDownloadLogsCollection() {
  const db = await getDatabase();
  return db.collection('downloadLogs');
}

export async function getUploadProgressCollection() {
  const db = await getDatabase();
  return db.collection('uploadProgress');
}

export async function getSupportRequestsCollection() {
  const db = await getDatabase();
  return db.collection('supportRequests');
}

// Re-export plan configurations for server-side use
export { PLAN_CONFIGS } from './plans';