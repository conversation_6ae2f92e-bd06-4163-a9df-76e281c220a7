"use client";

import React, { useRef, useState } from "react";
import { chunkedUploadFile } from "@/app/lib/chunkedUploader";
import { Progress } from "@/app/components/ui/progress";

const BYTES_IN_MB = 1024 * 1024;
const STANDARD_CAP_MB = 4; // 4MB limit for Vercel API routes
const STANDARD_CAP_BYTES = STANDARD_CAP_MB * BYTES_IN_MB;

type UploadResponse = {
  success?: boolean;
  data?: any;
  error?: string;
  message?: string;
};

type UploadStatus = "queued" | "uploading" | "completed" | "error";

type FileEntry = {
  id: string;
  file: File;
  progress: number; // 0-100
  status: UploadStatus;
  speed?: string;
  eta?: string;
  error?: string;
};

function formatBytesPerSec(bps: number) {
  if (!isFinite(bps) || bps <= 0) return "0 B/s";
  const units = ["B/s", "KB/s", "MB/s", "GB/s"];
  let u = 0;
  let n = bps;
  while (n >= 1024 && u < units.length - 1) {
    n /= 1024;
    u++;
  }
  return `${n.toFixed(n < 10 ? 2 : 1)} ${units[u]}`;
}

function StatusBadge({ status }: { status: UploadStatus }) {
  if (status === "uploading") {
    return (
      <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-blue-100 text-blue-800 border border-blue-200">
        <svg className="animate-spin -ml-0.5 mr-1 h-3 w-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v3A5 5 0 007 12H4z"></path>
        </svg>
        Processing
      </span>
    );
  }
  if (status === "queued") {
    return (
      <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-700 border border-gray-200">
        <svg className="h-3.5 w-3.5 mr-1 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
          <path d="M12 2a10 10 0 1010 10A10.011 10.011 0 0012 2zm.75 5.25a.75.75 0 00-1.5 0V12a.75.75 0 00.22.53l3 3a.75.75 0 001.06-1.06l-2.78-2.78z" />
        </svg>
        Queued
      </span>
    );
  }
  if (status === "completed") {
    return (
      <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-green-100 text-green-800 border border-green-200">
        Completed
      </span>
    );
  }
  return (
    <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-red-100 text-red-800 border border-red-200">
      Error
    </span>
  );
}

export default function UploadForm() {
  // Multi-file queue state
  const [entries, setEntries] = useState<FileEntry[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [status, setStatus] = useState<string>("");

  // speed/eta tracking per active file
  const startTimeRef = useRef<number | null>(null);

  function addSelectedFiles(list: FileList | null) {
    if (!list || list.length === 0) return;
    const now = Date.now();
    const newEntries: FileEntry[] = Array.from(list).map((file, idx) => ({
      id: `${now}-${idx}-${file.name}`,
      file,
      progress: 0,
      status: "queued",
    }));
    setEntries((prev) => [...prev, ...newEntries]);
  }

  function updateEntry(id: string, patch: Partial<FileEntry>) {
    setEntries((prev) => prev.map((e) => (e.id === id ? { ...e, ...patch } : e)));
  }

  async function uploadSingle(entry: FileEntry) {
    // chunked uploader path first
    try {
      const file = entry.file;
      startTimeRef.current = Date.now();

      let json: UploadResponse | null = await chunkedUploadFile(file, {
        chunkSize: 8 * 1024 * 1024,
        threshold: 8 * 1024 * 1024,
        onProgress: (p) => {
          updateEntry(entry.id, { progress: p });
        },
        onChunk: ({ uploadedBytes }) => {
          const now = Date.now();
          const startMillis = startTimeRef.current ?? now;
          const elapsedSec = (now - startMillis) / 1000;
          const avgBps = elapsedSec > 0 ? uploadedBytes / elapsedSec : 0;
          const speed = formatBytesPerSec(avgBps);
          const remaining = Math.max(0, file.size - uploadedBytes);
          const etaSec = avgBps > 0 ? remaining / avgBps : 0;
          const eta =
            isFinite(etaSec) && etaSec > 0
              ? (() => {
                  const m = Math.floor(etaSec / 60);
                  const s = Math.round(etaSec % 60);
                  return m > 0 ? `${m}m ${s}s` : `${s}s`;
                })()
              : "";
          updateEntry(entry.id, { speed, eta });
        },
      });

      if (!json) {
        // Small file path: proxy upload with progress API
        const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const formData = new FormData();
        formData.append("file", file);
        formData.append("uploadId", uploadId);

        await new Promise<UploadResponse>((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          startTimeRef.current = Date.now();

          xhr.upload.onprogress = (ev: ProgressEvent) => {
            const loaded = ev.loaded;
            const total = ev.lengthComputable ? ev.total : file.size;
            const pct = total > 0 ? Math.min(100, Math.round((loaded / total) * 100)) : 0;
            updateEntry(entry.id, { progress: pct });

            // speed + eta
            const now = Date.now();
            const startMillis = startTimeRef.current!;
            const elapsedSec = (now - startMillis) / 1000;
            const avgBps = elapsedSec > 0 ? loaded / elapsedSec : 0;
            const speed = formatBytesPerSec(avgBps);
            const remaining = Math.max(0, total - loaded);
            const etaSec = avgBps > 0 ? remaining / avgBps : 0;
            const eta =
              isFinite(etaSec) && etaSec > 0
                ? (() => {
                    const m = Math.floor(etaSec / 60);
                    const s = Math.round(etaSec % 60);
                    return m > 0 ? `${m}m ${s}s` : `${s}s`;
                  })()
                : "";
            updateEntry(entry.id, { speed, eta });
          };

          xhr.onload = () => {
            try {
              const text = xhr.responseText;
              let json: UploadResponse;
              try {
                json = JSON.parse(text);
              } catch {
                json = { success: xhr.status >= 200 && xhr.status < 300, data: { raw: text } };
              }
              resolve(json);
            } catch (err) {
              reject(err);
            }
          };

          xhr.onerror = () => reject(new Error("Network error during upload"));
          xhr.onabort = () => reject(new Error("Upload aborted"));

          xhr.open("POST", "/api/upload-with-progress", true);
          xhr.send(formData);
        }).then((j) => {
          json = j;
        });
      }

      if (json?.success === false) {
        const errMsg = json.error || json.message || "Upload failed";
        updateEntry(entry.id, { status: "error", error: errMsg, progress: 0, speed: "", eta: "" });
        return;
      }

      updateEntry(entry.id, { status: "completed", progress: 100, speed: "", eta: "" });
    } catch (e: any) {
      updateEntry(entry.id, { status: "error", error: e?.message || "Upload failed", speed: "", eta: "" });
    }
  }

  async function startSequentialUpload(e?: React.FormEvent) {
    if (e) e.preventDefault();
    if (isUploading) return;
    if (entries.length === 0) {
      setStatus("Vælg en eller flere filer først");
      return;
    }
    setStatus("");
    setIsUploading(true);

    // Ensure queued statuses for any fresh items
    setEntries((prev) => prev.map((p) => (p.status === "completed" || p.status === "uploading" ? p : { ...p, status: "queued" })));

    try {
      for (const entry of entries) {
        // Only process queued files; skip completed or errored
        if (entry.status !== "queued") continue;

        updateEntry(entry.id, { status: "uploading", speed: "", eta: "" });
        await uploadSingle({ ...entry, status: "uploading" });
      }
    } finally {
      setIsUploading(false);
    }
  }

  function onInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    addSelectedFiles(e.target.files);
    // reset input so same files can be re-selected if needed
    e.currentTarget.value = "";
  }

  return (
    <div className="w-full max-w-xl space-y-4">
      <form onSubmit={startSequentialUpload} className="space-y-3">
        <div className="flex items-center gap-3">
          <input
            type="file"
            multiple
            onChange={onInputChange}
            className="block w-full text-sm text-gray-900 file:mr-4 file:rounded-md file:border-0 file:bg-blue-600 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-white hover:file:bg-blue-500"
          />
          <button
            type="submit"
            disabled={entries.length === 0 || isUploading}
            className="rounded-md bg-black px-4 py-2 text-white disabled:opacity-50"
          >
            {isUploading ? "Uploader..." : "Start upload"}
          </button>
        </div>

        {entries.length > 0 && (
          <p className="text-xs text-gray-600">
            {entries.length} fil{entries.length > 1 ? "er" : ""} i kø. Filer over 4MB bruger chunked upload.
          </p>
        )}
      </form>

      {entries.length > 0 && (
        <div className="space-y-3">
          {entries.map((f) => (
            <div key={f.id} className="flex items-center justify-between rounded border p-3">
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <span className="truncate font-medium">{f.file.name}</span>
                  <StatusBadge status={f.status} />
                </div>
                <div className="mt-2">
                  <Progress value={f.progress} />
                </div>
                <div className="mt-1 flex justify-between text-[11px] text-gray-600">
                  <span>{f.progress}%</span>
                  <span>
                    {f.status === "uploading" && f.speed ? f.speed : ""}
                    {f.status === "uploading" && f.eta ? ` • ETA ${f.eta}` : ""}
                  </span>
                </div>
                {f.status === "error" && f.error && (
                  <p className="mt-1 text-xs text-red-600">{f.error}</p>
                )}
              </div>
              <div className="ml-3 shrink-0 text-xs text-gray-500">
                {(f.file.size / (1024 * 1024)).toFixed(2)} MB
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="text-xs text-gray-500">
        Filer over 4MB uploades stykvis (8 MB chunks) direkte til GoFile.
        Alle filer gemmes sikkert i vores database med korrekt tracking.
      </div>
    </div>
  );
}