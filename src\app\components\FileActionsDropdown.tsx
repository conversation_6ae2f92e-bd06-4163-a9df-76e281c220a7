"use client";

import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { But<PERSON> } from "@/app/components/ui/button";
import { MoreVertical, Copy, Trash2, Edit, Save, X } from "lucide-react";
import { FileRecord } from "@/app/lib/types";
import DeleteConfirmationModal from "@/app/components/DeleteConfirmationModal";

export interface FileActionsDropdownProps {
  file: FileRecord;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCopyLink: (file: FileRecord) => void;
  onDelete: (file: FileRecord) => void;
  onEditLimit: () => void;
  onSaveLimit: (fileId: string) => void;
  onCancelLimit: () => void;
  isEditing: boolean;
  editingValue: string;
  setEditingValue: (value: string) => void;
  savingLimit: boolean;
}

/**
 * Themed actions dropdown for a file row, rendered in a portal (body)
 * so it overlays parent containers and avoids inner scrolling/clipping.
 */
export default function FileActionsDropdown({
  file,
  open,
  onOpenChange,
  onCopyLink,
  onDelete,
  onEditLimit,
  onSaveLimit,
  onCancelLimit,
  isEditing,
  editingValue,
  setEditingValue,
  savingLimit,
}: FileActionsDropdownProps) {
  const btnRef = useRef<HTMLButtonElement | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);

  // Inline confirm UI removed; we now use modals
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Close on outside click or Escape
  useEffect(() => {
    // Use pointerdown to run before onClick, ensuring correct toggle behavior
    const handlePointerDown = (event: MouseEvent) => {
      const target = event.target as Node;

      // If clicking trigger button:
      if (btnRef.current && btnRef.current.contains(target)) {
        // Toggle immediately based on current state
        onOpenChange(!open);
        // Prevent the subsequent onClick/open handlers from re-opening
        event.preventDefault();
        event.stopPropagation();
        return;
      }

      if (!open) return;

      // Clicks inside the menu should not close
      if (menuRef.current && menuRef.current.contains(target)) return;

      // Otherwise close
      onOpenChange(false);
    };

    const handleKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        // If modal is open, let the modal's own handler close it.
        if (showDeleteModal) return;
        onOpenChange(false);
      }
    };

    // Use pointerdown capturing to avoid focus/blur races and let menu buttons work
    document.addEventListener("pointerdown", handlePointerDown as any, true);
    document.addEventListener("keydown", handleKey);
    return () => {
      document.removeEventListener("pointerdown", handlePointerDown as any, true);
      document.removeEventListener("keydown", handleKey);
    };
  }, [open, onOpenChange, showDeleteModal]);

  // Position the floating menu anchored to the trigger button
  useLayoutEffect(() => {
    if (!open || !btnRef.current || !menuRef.current) return;
    const btnRect = btnRef.current.getBoundingClientRect();
    const menuEl = menuRef.current;

    // Default position: below-right of button
    let top = btnRect.bottom + window.scrollY + 12; // gap allows arrow
    let left = btnRect.right + window.scrollX - 224; // menu width ~ 224px (w-56)

    // Keep within viewport horizontally
    const viewportWidth = window.innerWidth;
    if (left + 224 + 12 > viewportWidth) {
      left = Math.max(8, viewportWidth - 224 - 8);
    }
    if (left < 8) left = 8;

    // If not enough space below, flip to above
    const viewportHeight = window.innerHeight;
    const estimatedHeight = 160; // allow a bit more for arrow
    const placeAbove = btnRect.bottom + 12 + estimatedHeight > viewportHeight;

    if (placeAbove) {
      top = btnRect.top + window.scrollY - estimatedHeight - 12;
      (menuEl as any).dataset.placement = "top";
    } else {
      (menuEl as any).dataset.placement = "bottom";
    }

    menuEl.style.position = "absolute";
    menuEl.style.top = `${top}px`;
    menuEl.style.left = `${left}px`;
    menuEl.style.zIndex = "1000";

    // Compute arrow X offset to point to button center
    const arrow = menuEl.querySelector<HTMLDivElement>("[data-ff-arrow]");
    if (arrow) {
      const buttonCenter = btnRect.left + btnRect.width / 2;
      const arrowLeft = buttonCenter - left - 8; // 8px half arrow size approx
      // Keep arrow within container padding (min 12px, max width-12px)
      const minX = 12;
      const maxX = 224 - 12;
      arrow.style.left = `${Math.max(minX, Math.min(arrowLeft, maxX))}px`;
    }
  }, [open]);


  const menu = open ? (
    <div ref={menuRef} role="menu" aria-label="Filhandlinger" className="w-56">
      <div className="relative">
        {/* outer glow */}
        <div className="absolute -inset-0.5 bg-gradient-to-b from-blue-600/10 to-blue-700/10 rounded-2xl blur-sm"></div>

        {/* arrow (uses ::before to create the white panel arrow, and an outer glow layer) */}
        <div
          data-ff-arrow
          className="absolute -top-2 h-4 w-4 rotate-45"
          style={{ display: 'block' }}
        >
          {/* glow for arrow */}
          <div className="absolute inset-0 -z-10 rounded-sm bg-gradient-to-b from-blue-600/10 to-blue-700/10 blur-[2px]"></div>
          {/* arrow body - matches panel bg and border */}
          <div className="absolute inset-[2px] rounded-[2px] bg-white/90 backdrop-blur-xl border border-blue-100"></div>
        </div>

        {/* flip arrow to bottom when placed above */}
        <style>{`
          [data-placement="top"] [data-ff-arrow] {
            top: auto;
            bottom: -8px;
            transform: rotate(45deg);
          }
        `}</style>

        {/* panel */}
        <div className="relative rounded-2xl overflow-hidden border border-blue-100 bg-white/90 backdrop-blur-xl shadow-xl dark:border-blue-200">
          <div className="p-2">

            {/* Copy link */}
            <button
              type="button"
              role="menuitem"
              onMouseDown={(e) => e.stopPropagation()}
              onClick={(e) => {
                e.stopPropagation();
                onCopyLink(file);
                onOpenChange(false);
              }}
              className="group w-full flex items-center gap-3 px-3 py-2 rounded-xl text-sm font-medium text-gray-800 hover:text-blue-700 hover:bg-blue-50 transition-all"
            >
              <span className="inline-flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 text-blue-700 group-hover:bg-blue-200 group-hover:scale-105 transition-transform">
                <Copy className="h-4 w-4" />
              </span>
              <div className="flex-1 text-left">Kopier download link</div>
            </button>

            <div className="my-2 h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent" />

            {/* Edit download limit */}
            <button
              type="button"
              role="menuitem"
              onMouseDown={(e) => e.stopPropagation()}
              onClick={(e) => {
                e.stopPropagation();
                onEditLimit();
                onOpenChange(false);
              }}
              className="group w-full flex items-center gap-3 px-3 py-2 rounded-xl text-sm font-medium text-gray-800 hover:text-blue-700 hover:bg-blue-50 transition-all"
            >
              <span className="inline-flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 text-blue-700 group-hover:bg-blue-200 group-hover:scale-105 transition-transform">
                <Edit className="h-4 w-4" />
              </span>
              <div className="flex-1 text-left">Rediger download grænse</div>
            </button>

            <div className="my-2 h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent" />

            {/* Delete - opens modal (and close dropdown) */}
            <button
              type="button"
              role="menuitem"
              onMouseDown={(e) => e.stopPropagation()}
              onClick={(e) => {
                e.stopPropagation();
                // Close dropdown first, then open modal next tick for smooth UX
                onOpenChange(false);
                setTimeout(() => setShowDeleteModal(true), 0);
              }}
              className="group w-full flex items-center gap-3 px-3 py-2 rounded-xl text-sm font-bold text-red-600 hover:text-white hover:bg-gradient-to-b hover:from-red-600 hover:to-red-700 transition-all"
            >
              <span className="inline-flex h-8 w-8 items-center justify-center rounded-lg bg-red-100 text-red-700 group-hover:bg-white/20 group-hover:text-white transition-colors">
                <Trash2 className="h-4 w-4" />
              </span>
              <div className="flex-1 text-left">Slet fil</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  ) : null;

  return (
    <div className="relative">
      <Button
        ref={btnRef}
        aria-haspopup="menu"
        aria-expanded={open}
        aria-label="Åbn handlinger"
        variant="ghost"
        size="sm"
        // No-op: toggle is handled in capturing mousedown to avoid race conditions with portals
        onClick={(e) => {
          // onClick remains a no-op to avoid double toggling with pointerdown
          e.preventDefault();
          e.stopPropagation();
        }}
        className="hover:bg-blue-50 text-gray-900 hover:text-blue-700 dark:hover:bg-blue-50/70 rounded-lg"
      >
        <MoreVertical className="h-4 w-4" />
      </Button>

      {/* Render menu into body to escape clipping/scroll of table/card */}
      {open && typeof window !== "undefined"
        ? createPortal(menu, document.body)
        : null}

      {/* Delete confirmation modal */}
      <DeleteConfirmationModal
        open={showDeleteModal}
        title="Er du sikker?"
        description="Denne handling kan ikke fortrydes. Vil du slette filen?"
        confirmText="Slet"
        cancelText="Annuller"
        onCancel={() => setShowDeleteModal(false)}
        onConfirm={() => {
          onDelete(file);
          setShowDeleteModal(false);
        }}
      />

    </div>
  );
}