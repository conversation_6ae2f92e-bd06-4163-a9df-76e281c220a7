export const metadata = {
  title: "Kontakt | FlyFiles",
  description:
    "Kontakt FlyFiles – dansk support, hurtig hjælp og enkle svar. Skriv til os via formula<PERSON> eller find vores kontaktoplysninger her.",
}

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { Mail, Phone, MapPin, Clock, Send, ChevronDown } from "lucide-react"

// NOTE: Avoid passing event handlers from a Server Component.
// We keep the form non-interactive here (no onSubmit). The submit button is type="button" and triggers a client-side alert via data attribute.

// Client Component extracted into its own file to allow metadata export here
import ContactForm from "./subject-form"

export default function KontaktPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
              Dansk support – nemt og hurtigt
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight">
              Kontakt <span className="text-blue-300">os</span>
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 mb-0 max-w-2xl mx-auto">
              Har du spørgsmål, brug for hjælp eller forslag? Skriv til os – vi svarer hurtigt.
            </p>
          </div>
        </div>
      </section>

      {/* Content grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact form (UI-only) */}
          <div className="lg:col-span-2">
            <Card className="border-blue-100 bg-white">
              <CardHeader>
                <CardTitle className="text-2xl text-gray-800">Send en besked</CardTitle>
                <CardDescription className="text-gray-600">
                  Udfyld formularen – vi vender tilbage så hurtigt som muligt.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Split the interactive form into a small client component to satisfy RSC rules */}
                <ContactForm />
              </CardContent>
            </Card>
          </div>

          {/* Contact info */}
          <div className="space-y-6">
            <Card className="border-blue-100 bg-white">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Kontaktoplysninger</CardTitle>
                <CardDescription className="text-gray-600">
                  Alternativer til formularen – vælg det, der passer dig bedst.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 text-gray-700">
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-colors"
                >
                  <div className="mt-0.5 text-blue-700">
                    <Mail className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">E-mail</div>
                    <div className="text-sm text-gray-600"><EMAIL></div>
                  </div>
                </a>


                <div className="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-colors">
                  <div className="mt-0.5 text-blue-700">
                    <MapPin className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">Adresse</div>
                    <div className="text-sm text-gray-600">Danmark – online platform</div>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-3 rounded-lg hover:bg-blue-50 transition-colors">
                  <div className="mt-0.5 text-blue-700">
                    <Clock className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">Supporttider</div>
                    <div className="text-sm text-gray-600">Svar typisk inden for 24 timer på hverdage</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-blue-100 bg-white">
              <CardHeader>
                <CardTitle className="text-xl text-gray-800">Andre ressourcer</CardTitle>
                <CardDescription className="text-gray-600">
                  Se også disse sider, som ofte besvarer spørgsmål hurtigt.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 text-gray-700">
                <a href="/pricing" className="block p-3 rounded-lg hover:bg-blue-50 transition-colors">
                  Priser og planer
                </a>
                <a href="/hvorfor-os" className="block p-3 rounded-lg hover:bg-blue-50 transition-colors">
                  Hvorfor vælge FlyFiles?
                </a>
                <a href="/bliv-partner" className="block p-3 rounded-lg hover:bg-blue-50 transition-colors">
                  Bliv partner
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  )
}