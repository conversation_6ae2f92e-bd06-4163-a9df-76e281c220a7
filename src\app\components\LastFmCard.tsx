import Image from 'next/image';

export type LastFmCardProps = {
  data: {
    isPlaying: boolean;
    title: string | null;
    artists: string | null;
    album: string | null;
    image: string | null;
    url: string | null;
    playedAt?: string | null;
  } | null;
};

export default function LastFmCard({ data }: LastFmCardProps) {
  const isEmpty = !data || !data.title;

  return (
    <div className="mt-6 flex items-center gap-4 rounded-xl border border-blue-100 bg-white p-4 shadow-sm">
      {data?.image ? (
        <div className="relative h-16 w-16 overflow-hidden rounded-md bg-gray-100">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img src={data.image} alt="Album art" className="h-full w-full object-cover" />
        </div>
      ) : (
        <div className="h-16 w-16 rounded-md bg-blue-100/60 flex items-center justify-center text-blue-600 font-semibold">
          ♫
        </div>
      )}
      <div className="min-w-0 flex-1">
        <div className="text-sm text-gray-500">{data?.isPlaying ? 'Lytter lige nu' : 'Sidst lyttet'}</div>
        {isEmpty ? (
          <div className="mt-0.5 text-gray-700 font-medium">Ingen data tilgængelig</div>
        ) : (
          <div className="mt-0.5 truncate">
            <a
              href={data?.url ?? '#'}
              target="_blank"
              rel="noreferrer"
              className="font-semibold text-gray-900 hover:text-blue-700"
            >
              {data?.title}
            </a>
            {data?.artists && (
              <span className="text-gray-600"> — {data.artists}</span>
            )}
            {data?.album && (
              <span className="ml-1 text-gray-500">({data.album})</span>
            )}
          </div>
        )}
        {!data?.isPlaying && data?.playedAt && (
          <div className="text-xs text-gray-400 mt-0.5">{new Date(data.playedAt).toLocaleString()}</div>
        )}
      </div>
      {data?.url ? (
        <a
          href={data.url}
          target="_blank"
          rel="noreferrer"
          className="ml-auto inline-flex h-8 items-center rounded-md border border-blue-200 px-3 text-sm text-blue-700 hover:bg-blue-50"
        >
          Åbn
        </a>
      ) : null}
    </div>
  );
}
