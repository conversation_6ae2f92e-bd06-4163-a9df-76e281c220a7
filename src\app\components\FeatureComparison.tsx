"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, Check } from "lucide-react";

type Row = {
  title: string;
  description: string;
  flyfiles: "yes" | "no" | string;
  other: string;
};

export type FeatureComparisonProps = {
  title?: string;
  subtitle?: string;
  rows: Row[];
  initialVisibleRows?: number; // how many rows to show before collapse
  otherLabel?: string; // label for the comparison column (e.g., "WeTransfer (gratis)")
  className?: string;
};

/**
 * FeatureComparison.tsx
 * A collapsible comparison component that initially shows a subset of rows ("half" by default),
 * with a gradient fade and an arrow button to expand/collapse.
 */
export default function FeatureComparison({
  title = "Hvorfor vælge FlyFiles frem for andre?",
  subtitle = "Sammenligning af nøglefunktioner baseret på vores implementerede funktioner og offentlige information.",
  rows,
  initialVisibleRows,
  otherLabel = "Alternativ",
  className = "",
}: FeatureComparisonProps) {
  const computedInitial = initialVisibleRows ?? Math.max(1, Math.floor(rows.length / 2));
  const [expanded, setExpanded] = useState(false);

  const visibleRows = expanded ? rows : rows.slice(0, computedInitial);

  return (
    <section className={className}>
      <div className="text-center mb-8">
        <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">
          {title.split("FlyFiles").length > 1 ? (
            <>
              {title.split("FlyFiles")[0]}
              <span className="text-blue-700">FlyFiles</span>
              {title.split("FlyFiles")[1]}
            </>
          ) : (
            title
          )}
        </h2>
        {subtitle && <p className="mt-3 text-gray-600">{subtitle}</p>}
      </div>

      {/* Remove overflow clip so the button can sit above without being cut off */}
      <div className="relative overflow-visible rounded-2xl border border-gray-200 bg-white shadow-sm">
        <div className="grid grid-cols-12 bg-gray-50 px-4 py-3 text-xs font-semibold uppercase tracking-wide text-gray-600">
          <div className="col-span-5">Funktion</div>
          <div className="col-span-3 text-center">FlyFiles</div>
          <div className="col-span-4 text-center">{otherLabel}</div>
        </div>

        <ul className="divide-y divide-gray-100">
          {visibleRows.map((r, idx) => (
            <li key={idx} className="grid grid-cols-12 items-center px-4 py-4">
              <div className="col-span-5">
                <div className="font-medium text-gray-900">{r.title}</div>
                <p className="text-sm text-gray-600">{r.description}</p>
              </div>
              <div className="col-span-3 flex items-center justify-center">
                {r.flyfiles === "yes" ? (
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-1 text-xs font-semibold text-green-700">
                    <Check className="mr-1 h-3.5 w-3.5" /> Ja
                  </span>
                ) : r.flyfiles === "no" ? (
                  <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-1 text-xs font-semibold text-red-700">
                    Nej
                  </span>
                ) : (
                  <span className="text-sm text-gray-700">{r.flyfiles}</span>
                )}
              </div>
              <div className="col-span-4 text-center text-sm text-gray-700">{r.other}</div>
            </li>
          ))}
        </ul>

        {!expanded && rows.length > computedInitial && (
          <>
            {/* Make sure nothing can overlap the button: raise z-index and disable overflow clipping */}
            <div className="pointer-events-none absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-white to-transparent" style={{ zIndex: 10 }} />
            <div className="absolute inset-x-0 -bottom-5 flex items-end justify-center" style={{ zIndex: 50 }}>
              <button
                type="button"
                onClick={() => setExpanded((v) => !v)}
                className="relative z-50 inline-flex items-center gap-2 rounded-xl border border-blue-200 bg-white px-4 py-2 text-sm font-semibold text-blue-700 shadow-sm transition-all hover:border-blue-300 hover:bg-blue-50"
                aria-expanded={expanded}
              >
                <>
                  Vis flere
                  <ChevronDown className="h-4 w-4" />
                </>
              </button>
            </div>
          </>
        )}
      </div>

      {/* Expanded state: position 'Vis mindre' exactly like 'Vis flere' at the card edge, never underneath */}
      {rows.length > computedInitial && expanded && (
        <div className="relative">
          {/* Anchor to the same bottom edge and stacking context as the collapsed-state button */}
          <div className="absolute inset-x-0 -bottom-5 flex items-end justify-center" style={{ zIndex: 50 }}>
            <button
              type="button"
              onClick={() => setExpanded((v) => !v)}
              className="relative z-50 inline-flex items-center gap-2 rounded-xl border border-blue-200 bg-white px-4 py-2 text-sm font-semibold text-blue-700 shadow-sm transition-all hover:border-blue-300 hover:bg-blue-50"
              aria-expanded={expanded}
            >
              <>
                Vis mindre
                <ChevronUp className="h-4 w-4" />
              </>
            </button>
          </div>
          {/* Provide the same reserved space as collapsed spacer to avoid overlap with following content */}
          <div className="h-10" />
        </div>
      )}
    </section>
  );
}