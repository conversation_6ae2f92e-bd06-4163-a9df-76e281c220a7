"use client"

import { ChevronDown } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

type SecondaryOptionsMap = Record<string, string[]>

type SupportRequestPayload = {
  name: string
  email: string
  subject: string
  subjectDetail?: string
  contactMethod: 'email' | 'here'
  message: string
}

export default function ContactForm() {
  const { data: session, status } = useSession()

  // Primary dropdown state
  const [isOpen, setIsOpen] = useState(false)
  const [subject, setSubject] = useState("")
  const [customSubject, setCustomSubject] = useState("")
  const [useOther, setUseOther] = useState(false)

  // Secondary dropdown state
  const [isSecondaryOpen, setIsSecondaryOpen] = useState(false)
  const [secondary, setSecondary] = useState("")

  // Contact method dropdown state
  const [isMethodOpen, setIsMethodOpen] = useState(false)
  const [contactMethod, setContactMethod] = useState<"email" | "here" | "">("")

  // Prefill name/email when user is logged in
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const isAuthenticated = status === "authenticated"

  // Existing requests when user chooses "here"
  const [requests, setRequests] = useState<any[]>([])
  const [loadingRequests, setLoadingRequests] = useState(false)

  // User files for deletion selection
  type UserFileSlim = {
    _id: string
    originalName: string
    size: number
    uploadDate: string | Date
    isActive: boolean
    expiryDate: string | Date
  }
  const [userFiles, setUserFiles] = useState<UserFileSlim[]>([])
  const [loadingFiles, setLoadingFiles] = useState(false)
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([])

  // Constants and derived state for deletion subject behavior
  const deletionSubjectLabel = "Slet filer permanent nu (før 10 dage)"
  const deleteAllSecondaryLabel = "Slet alle mine filer nu"
  const isDeletionSubject = !useOther && subject === deletionSubjectLabel
  const isDeleteAllSecondary = isDeletionSubject && secondary === deleteAllSecondaryLabel

  useEffect(() => {
    if (isAuthenticated) {
      const sessionName = session?.user?.name ?? ""
      const sessionEmail = session?.user?.email ?? ""
      setName((prev) => (prev ? prev : sessionName))
      setEmail((prev) => (prev ? prev : sessionEmail))
    }
  }, [isAuthenticated, session?.user?.name, session?.user?.email])

  // Check for URL query parameters to pre-fill subject
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const subjectParam = urlParams.get('subject')
      
      if (subjectParam === 'slet-filer') {
        setSubject("Slet filer permanent nu (før 10 dage)")
        setUseOther(false)
      }
    }
  }, [])

  const isNameLocked = isAuthenticated && !!(session?.user?.name)
  const isEmailLocked = isAuthenticated && !!(session?.user?.email)

  const options = [
    "Slet filer permanent nu (før 10 dage)",
    "Fakturering og betaling",
    "Teknisk support",
    "Spørgsmål til funktioner",
    "Samarbejde/partner",
    "Feedback",
  ]

  // Secondary options based on primary choice (excluding "Andet")
  const secondaryOptionsMap: SecondaryOptionsMap = useMemo(() => ({
    "Fakturering og betaling": [
      "Faktura mangler",
      "Forkert beløb",
      "Ændring af betalingsmetode",
      "Kvittering/ moms",
    ],
    "Teknisk support": [
      "Kan ikke uploade",
      "Fejl ved download",
      "Ydelsesproblem",
      "Anden teknisk fejl",
    ],
    "Spørgsmål til funktioner": [
      "Filbeskyttelse",
      "Deling/links",
      "Lagring og retention",
      "Andet om funktioner",
    ],
    "Slet filer permanent nu (før 10 dage)": [
      "Slet specifikke filer nu",
      "Slet alle mine filer nu",
    ],
    "Samarbejde/partner": [
      "Affiliate",
      "Forretningssamarbejde",
      "Andet",
    ],
    "Feedback": [
      "Forslag til forbedring",
      "Bug-rapport",
      "Brugeroplevelse",
      "Andet feedback",
    ],
  }), [])

  // When "Andet" is selected, show "Andet" as the selected label in the box.
  // The hidden input will still carry the actual value (custom or predefined) further below.
  const displaySubject = useOther ? "Andet" : subject

  const secondaryCandidates = !useOther && subject ? (secondaryOptionsMap[subject] || []) : []

  // Ensure secondary selection resets whenever primary subject changes
  useEffect(() => {
    setSecondary("")
    setIsSecondaryOpen(false)
  }, [subject, useOther])

  // Reset contact method if user logs out (prevent "here" when not authenticated)
  useEffect(() => {
    if (!isAuthenticated && contactMethod === "here") {
      setContactMethod("")
      setIsMethodOpen(false)
    }
  }, [isAuthenticated, contactMethod])

  // Fetch user's existing requests if they choose "here"
  useEffect(() => {
    const fetchRequests = async () => {
      if (contactMethod !== "here" || !isAuthenticated) {
        setRequests([])
        return
      }
      setLoadingRequests(true)
      try {
        const res = await fetch('/api/support', { method: 'GET' })
        if (res.ok) {
          const json = await res.json()
          setRequests(json.data || [])
        } else {
          setRequests([])
        }
      } catch {
        setRequests([])
      } finally {
        setLoadingRequests(false)
      }
    }
    fetchRequests()
  }, [contactMethod, isAuthenticated])

  // Fetch user's files (active + recently deactivated within 10 days)
  useEffect(() => {
    const fetchFiles = async () => {
      // Only fetch if the special deletion subject is chosen and user is authenticated
      if (!isDeletionSubject || !isAuthenticated) {
        setUserFiles([])
        setSelectedFileIds([])
        return
      }
      setLoadingFiles(true)
      try {
        // 1) Get active files via existing endpoint
        const resActive = await fetch('/api/files', { method: 'GET' })
        const active: UserFileSlim[] = resActive.ok ? (await resActive.json()).data || [] : []

        // 2) Get deactivated files within 10 days via a lightweight aggregation endpoint (reuse GET by passing a flag if available)
        // Since no direct API exists, derive from support-side by requesting server to expand in the future.
        // For now, we approximate by asking for all files via a temporary internal endpoint if present; else show only active.
        // Attempt optional endpoint /api/files?includeInactiveWithinDays=10 (non-breaking if not implemented)
        let inactiveRecent: UserFileSlim[] = []
        try {
          const resInactive = await fetch('/api/files?includeInactiveWithinDays=10', { method: 'GET' })
          if (resInactive.ok) {
            const json = await resInactive.json()
            inactiveRecent = Array.isArray(json.data) ? json.data.filter((f: any) => !f.isActive) : []
          }
        } catch {}

        // Merge and sort by uploadDate desc
        const merged = [...active, ...inactiveRecent].sort((a, b) => {
          const da = new Date(a.uploadDate as any).getTime()
          const db = new Date(b.uploadDate as any).getTime()
          return db - da
        })

        setUserFiles(merged)
      } catch {
        setUserFiles([])
      } finally {
        setLoadingFiles(false)
      }
    }
    fetchFiles()
  }, [subject, useOther, isAuthenticated])

  // When "Slet alle mine filer nu" is chosen, always select all files and prevent deselection
  useEffect(() => {
    if (isDeleteAllSecondary && userFiles.length > 0) {
      const allIds = userFiles.map((f) => f._id)
      setSelectedFileIds(allIds)
    }
  }, [isDeleteAllSecondary, userFiles])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const formEl = e.target as HTMLFormElement
    const actualSubject = useOther ? (customSubject || "Andet") : subject
    if (!actualSubject || !contactMethod) {
      toast.error("Udfyld venligst emne og kontaktmetode.")
      return
    }

    // Compose message when deletion subject is selected to include selected file IDs
    let composedMessage = formEl.message.value
    if (actualSubject === "Slet filer permanent nu (før 10 dage)") {
      const chosen = selectedFileIds
      if (!chosen || chosen.length === 0) {
        toast.error("Vælg mindst én fil, der skal slettes permanent.")
        return
      }
      composedMessage =
        `Anmodning om permanent sletning før 10 dage.\n` +
        `Valgte fil-ID'er:\n${chosen.join(', ')}\n\n` +
        `Besked:\n${formEl.message.value}`
    }

    const payload: SupportRequestPayload = {
      name,
      email,
      subject: actualSubject,
      subjectDetail: secondary || undefined,
      contactMethod: contactMethod as 'email' | 'here',
      message: composedMessage
    }

    try {
      const res = await fetch('/api/support', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })
      const json = await res.json()
      if (res.ok && json.success) {
        toast.success("Din besked er sendt. Tak for din henvendelse!")
        // Reset form after successful submit
        setSubject("")
        setCustomSubject("")
        setUseOther(false)
        setSecondary("")
        setContactMethod("")
        ;(e.target as HTMLFormElement).reset()

        // Refresh requests list if on-site mode
        if (isAuthenticated && payload.contactMethod === 'here') {
          try {
            const r = await fetch('/api/support')
            if (r.ok) {
              const j = await r.json()
              setRequests(j.data || [])
            }
          } catch {}
        }
      } else {
        toast.error(json?.error || "Kunne ikke sende beskeden.")
      }
    } catch (err) {
      toast.error("Kunne ikke sende beskeden.")
    }
  }

  return (
    <form
      className="space-y-5"
      onSubmit={handleSubmit}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Navn
          </label>
          <input
            id="name"
            name="name"
            type="text"
            required
            placeholder="Dit navn"
            value={name}
            onChange={(e) => setName(e.target.value)}
            readOnly={isNameLocked}
            aria-readonly={isNameLocked}
            className={`w-full rounded-lg border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${isNameLocked ? "bg-gray-100 text-gray-700 border-gray-300 cursor-default" : "border-gray-300"}`}
          />
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            E-mail
          </label>
          <input
            id="email"
            name="email"
            type="email"
            required
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            readOnly={isEmailLocked}
            aria-readonly={isEmailLocked}
            className={`w-full rounded-lg border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${isEmailLocked ? "bg-gray-100 text-gray-700 border-gray-300 cursor-default" : "border-gray-300"}`}
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Emne
        </label>

        <div className="relative">
          <button
            type="button"
            className="w-full flex items-center justify-between rounded-lg border border-gray-300 px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
            onClick={() => setIsOpen((v) => !v)}
            aria-haspopup="listbox"
            aria-expanded={isOpen}
          >
            <span className={`truncate ${displaySubject ? "text-gray-900" : "text-gray-500"}`}>
              {displaySubject || "Vælg et emne"}
            </span>
            <ChevronDown
              className={`h-4 w-4 text-gray-500 transition-transform duration-200 ease-out ${isOpen ? "rotate-180" : "rotate-0"}`}
            />
          </button>

          {isOpen && (
            <ul
              role="listbox"
              className="absolute z-10 mt-2 w-full max-h-56 overflow-auto rounded-lg border border-gray-200 bg-white shadow-lg focus:outline-none"
            >
              {options.map((opt) => (
                <li
                  role="option"
                  key={opt}
                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${subject === opt && !useOther ? "bg-blue-50" : ""}`}
                  onClick={() => {
                    setSubject(opt)
                    setUseOther(false)
                    setIsOpen(false)
                  }}
                >
                  {opt}
                </li>
              ))}
              <li
                role="option"
                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${useOther ? "bg-blue-50" : ""}`}
                onClick={() => {
                  setUseOther(true)
                  if (!customSubject) setCustomSubject("")
                  setSubject("")
                  setSecondary("")
                  setIsOpen(false)
                }}
              >
                Andet...
              </li>
            </ul>
          )}
        </div>

        {secondaryCandidates.length > 0 && (
          <div className="mt-3">
            <label className="block text sm font-medium text-gray-700 mb-1">
              Uddybning
            </label>
            <div className="relative">
              <button
                type="button"
                className="w-full flex items-center justify-between rounded-lg border border-gray-300 px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition"
                onClick={() => setIsSecondaryOpen((v) => !v)}
                aria-haspopup="listbox"
                aria-expanded={isSecondaryOpen}
              >
                <span className={`truncate ${secondary ? "text-gray-900" : "text-gray-500"}`}>
                  {secondary || "Vælg en uddybning"}
                </span>
                <ChevronDown
                  className={`h-4 w-4 text-gray-500 transition-transform duration-200 ease-out ${isSecondaryOpen ? "rotate-180" : "rotate-0"}`}
                />
              </button>

              {isSecondaryOpen && (
                <ul
                  role="listbox"
                  className="absolute z-10 mt-2 w-full max-h-56 overflow-auto rounded-lg border border-gray-200 bg-white shadow-lg focus:outline-none"
                >
                  {secondaryCandidates.map((opt) => (
                    <li
                      role="option"
                      key={opt}
                      className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${secondary === opt ? "bg-blue-50" : ""}`}
                      onClick={() => {
                        setSecondary(opt)
                        setIsSecondaryOpen(false)
                      }}
                    >
                      {opt}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        )}

        {useOther && (
          <div className="mt-3">
            <label htmlFor="custom-subject" className="block text-sm font-medium text-gray-700 mb-1">
              Skriv dit emne
            </label>
            <input
              id="custom-subject"
              name="customSubject"
              type="text"
              placeholder="Hvad drejer det sig om?"
              value={customSubject}
              onChange={(e) => setCustomSubject(e.target.value)}
              className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        )}

        <input
          type="hidden"
          name="subject"
          value={useOther ? (customSubject || "Andet") : subject}
        />
        {secondaryCandidates.length > 0 && (
          <input type="hidden" name="subjectDetail" value={secondary} />
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Hvordan vil du kontaktes?
        </label>

        <div className="relative">
          <button
            type="button"
            className={`w-full flex items-center justify-between rounded-lg border px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition ${!contactMethod ? "border-gray-300" : "border-gray-300"}`}
            onClick={() => setIsMethodOpen((v) => !v)}
            aria-haspopup="listbox"
            aria-expanded={isMethodOpen}
          >
            <span className={`truncate ${contactMethod ? "text-gray-900" : "text-gray-500"}`}>
              {contactMethod === "email"
                ? "E-mail"
                : contactMethod === "here"
                ? "Læs svar her på siden"
                : "Vælg kontaktmetode"}
            </span>
            <ChevronDown
              className={`h-4 w-4 text-gray-500 transition-transform duration-200 ease-out ${isMethodOpen ? "rotate-180" : "rotate-0"}`}
            />
          </button>

          {isMethodOpen && (
            <ul
              role="listbox"
              className="absolute z-10 mt-2 w-full max-h-56 overflow-auto rounded-lg border border-gray-200 bg-white shadow-lg focus:outline-none"
            >
              <li
                role="option"
                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${contactMethod === "email" ? "bg-blue-50" : ""}`}
                onClick={() => {
                  setContactMethod("email")
                  setIsMethodOpen(false)
                }}
              >
                E-mail
              </li>

              <li
                role="option"
                aria-disabled={!isAuthenticated}
                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${contactMethod === "here" ? "bg-blue-50" : ""} ${!isAuthenticated ? "text-gray-400 cursor-not-allowed" : ""}`}
                onClick={() => {
                  if (!isAuthenticated) return
                  setContactMethod("here")
                  setIsMethodOpen(false)
                }}
                title={!isAuthenticated ? "Log ind for at læse svar her på siden" : undefined}
              >
                Læs svar her på siden { !isAuthenticated ? "(kræver login)" : "" }
              </li>
            </ul>
          )}
        </div>

        {!isAuthenticated && (
          <p className="mt-2 text-xs text-gray-500">
            Du skal være logget ind for at kunne læse svaret her på siden. Vælg ellers E-mail.
          </p>
        )}

        <input type="hidden" name="contactMethod" value={contactMethod} />
      </div>

      <div>
        {subject === deletionSubjectLabel && (
          <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-3">
            <div className="text-sm font-semibold text-red-700 mb-1">
              Permanent sletning før 10 dage
            </div>
            <p className="text-xs text-red-700/90">
              Vælg de filer, der skal slettes med det samme. Du kan vælge aktive filer samt filer der stadigvæk ligger i systemet. Handling kan ikke fortrydes.
            </p>
    
            <div className="mt-3 rounded-md bg-white p-2 border border-red-100">
              {loadingFiles ? (
                <div className="text-sm text-gray-600 px-2 py-1">Indlæser filer...</div>
              ) : userFiles.length === 0 ? (
                <div className="text-sm text-gray-600 px-2 py-1">Ingen filer fundet.</div>
              ) : (
                <ul className="max-h-56 overflow-auto divide-y divide-gray-100">
                  {userFiles.map((f) => {
                    const isInactive = !f.isActive
                    const checked = selectedFileIds.includes(f._id)
                    const disableToggle = isDeleteAllSecondary
                    return (
                      <li key={f._id} className="flex items-center gap-3 px-2 py-2">
                        <input
                          id={`file-${f._id}`}
                          type="checkbox"
                          className="h-4 w-4"
                          checked={checked}
                          disabled={disableToggle}
                          aria-disabled={disableToggle}
                          title={disableToggle ? "Alle filer er valgt for sletning" : undefined}
                          onChange={(e) => {
                            if (disableToggle) return
                            const id = f._id
                            setSelectedFileIds((prev) =>
                              e.target.checked ? Array.from(new Set([...prev, id])) : prev.filter((x) => x !== id)
                            )
                          }}
                        />
                        <label htmlFor={`file-${f._id}`} className="flex-1 cursor-pointer">
                          <div className="text-sm text-gray-900">
                            {f.originalName}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(f.uploadDate).toLocaleString('da-DK')} • {Math.round((f.size || 0) / 1024)} KB • {isInactive ? 'Deaktiveret' : 'Aktiv'}
                          </div>
                        </label>
                        {isInactive && (
                          <span className="ml-2 inline-flex items-center rounded bg-yellow-100 px-2 py-0.5 text-xs font-medium text-yellow-800">
                            Stadigvæk i systemet
                          </span>
                        )}
                      </li>
                    )
                  })}
                </ul>
              )}
            </div>
          </div>
        )}
    
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
          Besked
        </label>
        <textarea
          id="message"
          name="message"
          required
          rows={6}
          placeholder="Skriv din besked her..."
          className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Existing requests list when user wants answers here */}
      {isAuthenticated && contactMethod === 'here' && (
        <div className="rounded-lg border border-blue-100 bg-blue-50 p-3 sm:p-4">
          <div className="text-sm font-medium text-gray-700 mb-2">Dine tidligere henvendelser</div>
          {loadingRequests ? (
            <div className="text-sm text-gray-600">Indlæser...</div>
          ) : requests.length === 0 ? (
            <div className="text-sm text-gray-600">Ingen henvendelser endnu.</div>
          ) : (
            <ul className="space-y-2">
              {requests.map((r) => (
                <li key={r._id} className="rounded-md bg-white p-3 border border-gray-200">
                  <div className="text-sm font-semibold text-gray-800">{r.subject}{r.subjectDetail ? ` – ${r.subjectDetail}` : ''}</div>
                  <div className="text-xs text-gray-500 mt-0.5">{new Date(r.createdAt).toLocaleString('da-DK')}</div>
                  <div className="text-sm text-gray-700 mt-2 whitespace-pre-wrap">{r.message}</div>
                  {Array.isArray(r.replies) && r.replies.length > 0 && (
                    <div className="mt-2 border-t border-gray-200 pt-2">
                      <div className="text-sm font-medium text-gray-700">Svar</div>
                      <ul className="mt-1 space-y-1">
                        {r.replies.map((rep: any, idx: number) => (
                          <li key={idx} className="text-sm text-gray-700">
                            <span className="font-semibold">{rep.by === 'support' ? 'Support' : 'Dig'}:</span> {rep.message}
                            <span className="ml-2 text-xs text-gray-500">{new Date(rep.createdAt).toLocaleString('da-DK')}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}

      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between rounded-lg border border-gray-200 bg-white/60 p-3 sm:p-4">
        <p className="text-xs sm:text-sm text-gray-600">
          Ved at sende accepterer du, at vi må kontakte dig angående din henvendelse.
        </p>
        <button
          type="submit"
          className="h-10 inline-flex items-center justify-center rounded-md bg-blue-600 px-5 sm:px-6 text-sm font-medium text-white shadow-sm transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <span className="flex items-center gap-2">
            Send besked
          </span>
        </button>
      </div>
    </form>
  )
}