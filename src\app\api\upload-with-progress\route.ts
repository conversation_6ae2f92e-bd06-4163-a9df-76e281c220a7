import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getFilesCollection, getUsersCollection, getSessionsCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'
import { 
  generateFileId, 
  getFileExpiryDate, 
  canUserUpload, 
  isValidFileType,
  getCurrentPeriodStart
} from '@/app/lib/utils'
import { ApiResponse, FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'
import { updateUploadProgress, deleteUploadProgress } from '@/app/lib/upload-progress'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
export const maxDuration = 300 // 5 minutes for large file uploads
export const preferredRegion = 'auto'

// For App Router, we need to handle large files differently
// The bodyParser config doesn't apply to App Router

export async function POST(request: NextRequest) {
  try {
    console.log('Upload-with-progress: Starting request')
    
    // Parse formData
    const formData = await request.formData()
    const file = formData.get('file') as unknown as File
    const uploadId = formData.get('uploadId') as string

    console.log('Upload-with-progress: Parsed formData', {
      hasFile: !!file,
      fileName: file?.name,
      fileSize: file?.size,
      fileSizeMB: Math.round((file?.size || 0) / 1024 / 1024),
      uploadId
    })

    if (!file) {
      console.log('Upload-with-progress: No file provided')
      return NextResponse.json(
        { success: false, error: 'No file provided' } as ApiResponse,
        { status: 400 }
      )
    }

    if (!uploadId) {
      console.log('Upload-with-progress: No uploadId provided')
      return NextResponse.json(
        { success: false, error: 'No uploadId provided' } as ApiResponse,
        { status: 400 }
      )
    }

    // Initialize progress
    console.log(`Upload: Initializing progress for ${uploadId}`)
    await updateUploadProgress(uploadId, 0, 'starting')

    // Check authentication or guest session
    const session = await auth()
    const userEmail = session?.user?.email || undefined
    const isAuthed = Boolean(userEmail)
    const guestCookie = request.cookies.get('guest_session_id')
    const sessionsCol = await getSessionsCollection()
    let guestSessionId = guestCookie?.value
    let user: any = null
    let effectivePlan: 'guest' | 'free' | 'upgrade1' | 'upgrade2' = 'guest'

    if (isAuthed) {
      const users = await getUsersCollection()
      user = await users.findOne({ email: userEmail })
      if (!user) {
        await updateUploadProgress(uploadId, 0, 'error', 'User not found')
        return NextResponse.json(
          { success: false, error: 'User not found' } as ApiResponse,
          { status: 404 }
        )
      }
      effectivePlan = (user.plan || 'free')
    } else {
      if (!guestSessionId) {
        // Create a new guest session if missing
        guestSessionId = `guest_${Math.random().toString(36).slice(2)}${Date.now().toString(36)}`
        await sessionsCol.insertOne({
          sessionId: guestSessionId,
          totalUploaded: 0,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          files: []
        })
      }
    }

    // Check upload limits
    const planKey = (effectivePlan in PLAN_CONFIGS ? effectivePlan : 'free') as keyof typeof PLAN_CONFIGS
    const planConfig = PLAN_CONFIGS[planKey]
    let currentUsage = 0
    if (isAuthed) {
      currentUsage = effectivePlan === 'free' ? user.usage.monthly : user.usage.weekly
    } else {
      const sessionDoc = await sessionsCol.findOne({ sessionId: guestSessionId })
      currentUsage = sessionDoc?.totalUploaded || 0
    }
    const uploadCheck = canUserUpload(planKey, currentUsage, file.size)
    
    if (!uploadCheck.canUpload) {
      await updateUploadProgress(uploadId, 0, 'error', uploadCheck.reason)
      return NextResponse.json(
        { success: false, error: uploadCheck.reason } as ApiResponse,
        { status: 400 }
      )
    }

    // Update progress to 10% - starting GoFile upload
    console.log(`Upload: Progress 10% for ${uploadId}`)
    await updateUploadProgress(uploadId, 10, 'uploading')

    // Get the best server for upload
    let uploadServer = 'store1.gofile.io'
    try {
      const serverResponse = await fetch('https://api.gofile.io/servers')
      const serverData = await serverResponse.json()

      if (serverData.status === 'ok' && serverData.data?.servers?.length > 0) {
        uploadServer = serverData.data.servers[0].name + '.gofile.io'
      }
    } catch (err) {
      console.log('Failed to get servers, using default:', err)
    }

    // Update progress to 20% - got server
    await updateUploadProgress(uploadId, 20, 'uploading')

    // Prepare FormData for GoFile using the same approach as the original upload route
    const goFileFormData = new FormData()
    goFileFormData.append('file', file)

    // Add authentication token and account ID
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN
    const accountId = process.env.GOFILE_ACCOUNT_ID

    if (accountToken) {
      goFileFormData.append('token', accountToken)
    }

    // Try to get the root folder for the account first
    let folderId = null
    if (accountToken && accountId) {
      try {
        const accountResponse = await fetch(`https://api.gofile.io/accounts/${accountId}?token=${accountToken}`)
        const responseText = await accountResponse.text()

        try {
          const accountData = JSON.parse(responseText)
          if (accountData.status === 'ok' && accountData.data?.rootFolder) {
            folderId = accountData.data.rootFolder
            goFileFormData.append('folderId', folderId)
          }
        } catch (parseError) {
          console.log('Failed to parse account response:', responseText)
        }
      } catch (err) {
        console.log('Failed to get account details:', err)
      }
    }

    // Update progress to 30% - prepared upload
    await updateUploadProgress(uploadId, 30, 'uploading')

    // Upload to GoFile with progress tracking
    const uploadUrl = `https://${uploadServer}/contents/uploadfile`
    
    // Use the same approach as the original upload route
    const uploadWithProgress = async () => {
      return new Promise((resolve, reject) => {
        // Start with 30% and gradually increase to 85% over time
        let progress = 30
        const startTime = Date.now()
        const estimatedUploadTime = Math.max(5000, file.size / 1000000 * 1000) // At least 5 seconds, or 1 second per MB
        
        const progressInterval = setInterval(async () => {
          const elapsed = Date.now() - startTime
          const progressPercent = Math.min(85, 30 + (elapsed / estimatedUploadTime) * 55)
          
          if (progressPercent > progress) {
            progress = Math.round(progressPercent)
            await updateUploadProgress(uploadId, progress, 'uploading')
          }
        }, 500) // Update every 500ms
        
        // Use the same approach as the original upload route
        const goFileResponse = fetch(uploadUrl, {
          method: 'POST',
          body: goFileFormData,
        })
        .then(async (response) => {
          clearInterval(progressInterval)
          console.log(`Upload: Fetch response status: ${response.status}`)
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
          
          const result = await response.json()
          console.log(`Upload: Fetch response:`, result)
          resolve(result)
        })
        .catch((error) => {
          clearInterval(progressInterval)
          console.error(`Upload: Fetch error:`, error)
          reject(error)
        })
      })
    }

    console.log(`Upload: Starting GoFile upload for ${uploadId}`)
    const goFileResult = await uploadWithProgress() as any
    console.log(`Upload: GoFile upload completed for ${uploadId}:`, goFileResult)
    
    // Update progress to 90% - upload completed, now processing
    await updateUploadProgress(uploadId, 90, 'processing')

    if (!goFileResult || goFileResult.status !== 'ok') {
      await updateUploadProgress(uploadId, 0, 'error', 'GoFile.io upload failed')
      return NextResponse.json(
        { success: false, error: 'GoFile.io upload failed', details: goFileResult } as ApiResponse,
        { status: 500 }
      )
    }

    // Create file record in database
    const fileId = generateFileId()
    const expiryDate = getFileExpiryDate(planKey as any)

    // Build the download link
    const originalNameSanitized = file.name.replace(/\//g, '_')
    let resolvedServer = goFileResult?.data?.server || uploadServer
    let resolvedFileIdUuid = goFileResult?.data?.id || goFileResult?.data?.fileId
    const shortCode = goFileResult?.data?.code || goFileResult?.data?.parentFolderCode

    // If we lack a UUID fileId, but have a short code, query GoFile getFile
    if (!resolvedFileIdUuid && shortCode) {
      try {
        const baseUrl = process.env.GOFILE_API_BASE_URL || 'https://api.gofile.io'
        const accountToken = process.env.GOFILE_ACCOUNT_TOKEN
        const infoUrl = `${baseUrl}/getFile/${shortCode}${accountToken ? `?token=${accountToken}` : ''}`
        const infoResp = await fetch(infoUrl, { method: 'GET' })
        const infoJson = await infoResp.json()
        
        if (infoResp.ok && infoJson?.status === 'ok' && infoJson?.data) {
          resolvedServer = infoJson.data.server || resolvedServer
          resolvedFileIdUuid = infoJson.data.fileId || resolvedFileIdUuid
        }
      } catch (e) {
        console.log('Failed to resolve GoFile file info via getFile:', e)
      }
    }

    // Construct direct link
    const safeServer = resolvedServer?.includes('gofile.io') ? resolvedServer : `${resolvedServer}.gofile.io`
    const constructedDirect = (safeServer && resolvedFileIdUuid)
      ? `https://${safeServer}/download/web/${resolvedFileIdUuid}/${encodeURIComponent(originalNameSanitized)}`
      : undefined

    const downloadUrl = goFileResult?.data?.directLink || constructedDirect

    if (!downloadUrl) {
      await updateUploadProgress(uploadId, 0, 'error', 'Failed to generate download URL')
      return NextResponse.json(
        { success: false, error: 'Failed to generate download URL' } as ApiResponse,
        { status: 500 }
      )
    }

    // Create file record
    const fileRecord: Omit<FileRecord, '_id'> & { _id: ObjectId } = {
      _id: new ObjectId(),
      filename: fileId,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      uploadDate: new Date(),
      expiryDate,
      downloadCount: 0,
      downloadLimit: -1, // Unlimited for now
      isActive: true,
      storageProvider: 'GF',
      directLink: downloadUrl,
      ...(isAuthed ? { ownerId: user._id.toString() } : { sessionId: guestSessionId! }),
    }

    const files = await getFilesCollection()
    await files.insertOne(fileRecord)

    // Update usage
    if (isAuthed) {
      const users = await getUsersCollection()
      const usageField = effectivePlan === 'free' ? 'usage.monthly' : 'usage.weekly'
      await users.updateOne(
        { _id: user._id },
        { $inc: { [usageField]: file.size }, $set: { [`${usageField}UpdatedAt`]: new Date() } }
      )
    } else {
      await sessionsCol.updateOne(
        { sessionId: guestSessionId },
        { $inc: { totalUploaded: file.size }, $push: { files: fileRecord._id.toString() } }
      )
    }

    // Update progress to 100% - completed
    await updateUploadProgress(uploadId, 100, 'completed')

    // Clean up progress record after a delay
    setTimeout(async () => {
      await deleteUploadProgress(uploadId)
    }, 5000) // Delete after 5 seconds

    const response = NextResponse.json({
      success: true,
      data: {
        fileId,
        downloadUrl,
        message: 'File uploaded successfully'
      }
    } as ApiResponse)
    // Set cookie if we created a guest session
    if (!isAuthed && guestCookie?.value !== guestSessionId) {
      response.cookies.set('guest_session_id', guestSessionId!, {
        httpOnly: true,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        path: '/',
        maxAge: 7 * 24 * 60 * 60
      })
    }
    return response

  } catch (error) {
    console.error('Upload-with-progress error:', error)
    const uploadId = request.nextUrl?.searchParams.get('uploadId')
    if (uploadId) {
      await updateUploadProgress(uploadId, 0, 'error', error instanceof Error ? error.message : 'Upload failed')
    }
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Upload failed' } as ApiResponse,
      { status: 500 }
    )
  }
}

 