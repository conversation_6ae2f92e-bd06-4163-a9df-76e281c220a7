import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "./components/ui/card";
import { But<PERSON> } from "./components/ui/button";

export default function NotFound() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero-like header to match other pages */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="grid gap-10 lg:grid-cols-2 items-center">
            <div>
              <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
                404 — Siden findes ikke
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight">
                Ups, vi kunne ikke finde siden
              </h1>
              <p className="text-lg sm:text-xl text-blue-100 max-w-xl">
                Den side du leder efter findes ikke, er blevet flyttet eller er midlertidigt utilgængelig.
              </p>
            </div>

            <div className="relative">
              <div className="scene perspective-1000">
                <div className="preserve-3d cube cube-rotate w-56 h-56 sm:w-64 sm:h-64 mx-auto">
                  <div className="cube-face cube-face-front absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center text-white font-bold text-5xl shadow-lg">
                    404
                  </div>
                  <div className="cube-face cube-face-back absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-700 to-blue-800 flex items-center justify-center text-white font-bold text-2xl">
                    FlyFiles
                  </div>
                  <div className="cube-face cube-face-right absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold">
                    Ikke fundet
                  </div>
                  <div className="cube-face cube-face-left absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold">
                    404
                  </div>
                  <div className="cube-face cube-face-top absolute inset-0 rounded-2xl bg-gradient-to-br from-sky-500 to-blue-600 flex items-center justify-center text-white font-semibold">
                    Ups!
                  </div>
                  <div className="cube-face cube-face-bottom absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-800 to-indigo-800 flex items-center justify-center text-white font-semibold">
                    Fejl
                  </div>
                </div>
              </div>
              <p className="mt-6 text-center text-blue-100">
                En lille animation for at gøre det lidt sjovere at fare vild.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Content section with actions and quick links */}
      <section className="py-10 sm:py-14">
        <div className="mx-auto w-full max-w-5xl px-4 sm:px-6 lg:px-8">
          <Card className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-2xl sm:text-3xl text-gray-800">Siden blev ikke fundet</CardTitle>
              <CardDescription className="text-base">
                Prøv at gå tilbage til forsiden, eller udforsk nogle af vores populære sider nedenfor.
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Kontroller venligst at adressen er korrekt.</li>
                <li>• Brug knapperne herunder for at komme videre.</li>
              </ul>
            </CardContent>
            <CardFooter className="flex flex-wrap gap-3">
              <Link href="/" className="inline-flex">
                <Button size="lg" className="gap-2">
                  Gå til forsiden
                  <span className="arrow-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M13 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </span>
                </Button>
              </Link>
              <Link href="/support" className="inline-flex">
                <Button variant="outline" size="lg">Kontakt support</Button>
              </Link>
              <Link href="/pricing" className="inline-flex">
                <Button variant="ghost" size="lg">Se priser</Button>
              </Link>
            </CardFooter>
          </Card>

          <div className="mt-10 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <Link href="/hvorfor-os" className="group">
              <Card className="p-5 hover:transform">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Hvorfor os</h3>
                    <p className="mt-1 text-sm text-gray-500">Forstå hvorfor virksomheder vælger FlyFiles</p>
                  </div>
                  <div className="arrow-wrapper text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M13 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Link>
            <Link href="/pricing" className="group">
              <Card className="p-5 hover:transform">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Priser</h3>
                    <p className="mt-1 text-sm text-gray-500">Enkel og gennemsigtig prismodel</p>
                  </div>
                  <div className="arrow-wrapper text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M13 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Link>
            <Link href="/bliv-partner" className="group">
              <Card className="p-5 hover:transform">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Bliv partner</h3>
                    <p className="mt-1 text-sm text-gray-500">Samarbejd med os om bedre fildeling</p>
                  </div>
                  <div className="arrow-wrapper text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M13 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}