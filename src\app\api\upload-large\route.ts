import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getUsersCollection, getSessionsCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'
import { 
  generateFileId, 
  getFileExpiryDate, 
  canUserUpload, 
  isValidFileType,
  getCurrentPeriodStart
} from '@/app/lib/utils'
import { ApiResponse, FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
export const maxDuration = 300
export const preferredRegion = 'auto'

export async function POST(request: NextRequest) {
  try {
    console.log('Upload-large: Starting request')
    
    const body = await request.json()
    const { filename, size, mimeType, uploadId } = body

    console.log('Upload-large: Parsed request', {
      filename,
      size,
      mimeType,
      uploadId
    })

    if (!filename || !size || !mimeType || !uploadId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' } as ApiResponse,
        { status: 400 }
      )
    }

    // Check authentication or guest
    const session = await auth()
    const userEmail = session?.user?.email || undefined
    const isAuthed = Boolean(userEmail)
    const guestCookie = request.cookies.get('guest_session_id')
    const sessionsCol = await getSessionsCollection()
    let guestSessionId = guestCookie?.value
    let user: any = null
    let effectivePlan: 'guest' | 'free' | 'upgrade1' | 'upgrade2' = 'guest'

    if (isAuthed) {
      const users = await getUsersCollection()
      user = await users.findOne({ email: userEmail })
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' } as ApiResponse,
          { status: 404 }
        )
      }
      effectivePlan = (user.plan || 'free')
    } else {
      if (!guestSessionId) {
        guestSessionId = `guest_${Math.random().toString(36).slice(2)}${Date.now().toString(36)}`
        await sessionsCol.insertOne({
          sessionId: guestSessionId,
          totalUploaded: 0,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          files: []
        })
      }
    }

    // Check upload limits
    let currentUsage = 0
    if (isAuthed) {
      currentUsage = effectivePlan === 'free' ? user.usage.monthly : user.usage.weekly
    } else {
      const sessionDoc = await sessionsCol.findOne({ sessionId: guestSessionId })
      currentUsage = sessionDoc?.totalUploaded || 0
    }
    const uploadCheck = canUserUpload((effectivePlan as any), currentUsage, size)
    
    if (!uploadCheck.canUpload) {
      return NextResponse.json(
        { success: false, error: uploadCheck.reason } as ApiResponse,
        { status: 400 }
      )
    }

    // Get the best server for upload
    let uploadServer = 'store1.gofile.io'
    try {
      const serverResponse = await fetch('https://api.gofile.io/servers')
      const serverData = await serverResponse.json()

      if (serverData.status === 'ok' && serverData.data?.servers?.length > 0) {
        uploadServer = serverData.data.servers[0].name + '.gofile.io'
      }
    } catch (err) {
      console.log('Failed to get servers, using default:', err)
    }

    // Get account token and folder ID
    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN
    const accountId = process.env.GOFILE_ACCOUNT_ID
    let folderId = null

    if (accountToken && accountId) {
      try {
        const accountResponse = await fetch(`https://api.gofile.io/accounts/${accountId}?token=${accountToken}`)
        const responseText = await accountResponse.text()

        try {
          const accountData = JSON.parse(responseText)
          if (accountData.status === 'ok' && accountData.data?.rootFolder) {
            folderId = accountData.data.rootFolder
          }
        } catch (parseError) {
          console.log('Failed to parse account response:', responseText)
        }
      } catch (err) {
        console.log('Failed to get account details:', err)
      }
    }

    // Return upload credentials for client-side upload
    const response = NextResponse.json({
      success: true,
      data: {
        uploadUrl: `https://${uploadServer}/contents/uploadfile`,
        token: accountToken,
        folderId,
        uploadId,
        filename,
        size,
        mimeType
      }
    } as ApiResponse)

    if (!isAuthed && guestCookie?.value !== guestSessionId) {
      response.cookies.set('guest_session_id', guestSessionId!, {
        httpOnly: true,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        path: '/',
        maxAge: 7 * 24 * 60 * 60
      })
    }

    return response

  } catch (error) {
    console.error('Upload-large error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Upload failed' } as ApiResponse,
      { status: 500 }
    )
  }
}
