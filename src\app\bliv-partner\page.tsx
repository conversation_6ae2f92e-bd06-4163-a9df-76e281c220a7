import { <PERSON>, Card<PERSON>ontent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"

export const metadata = {
  title: "Bliv partner | FlyFiles",
  description:
    "Bliv partner med FlyFiles – tjen på henvisninger, få adgang til værktøjer, materialer og dedikeret support. Sam<PERSON> leverer vi sikker, hurtig og dansk fildeling.",
}

export default function BlivPartnerPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-700 via-blue-800 to-blue-900 text-white">
        <div className="absolute inset-0 opacity-20 pointer-events-none">
          <div className="absolute -top-24 -left-16 h-72 w-72 rounded-full bg-blue-400 blur-3xl" />
          <div className="absolute top-1/3 -right-10 h-72 w-72 rounded-full bg-cyan-400 blur-3xl" />
          <div className="absolute bottom-0 left-1/4 h-56 w-56 rounded-full bg-indigo-500 blur-3xl" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 py-20 md:py-28">
          <div className="max-w-3xl">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold tracking-tight">
              Bliv partner med FlyFiles
            </h1>
            <p className="mt-4 text-blue-100 text-lg md:text-xl">
              Vær med til at udbrede Danmarks enkleste og hurtigste måde at dele store filer sikkert.
              Tjen på henvisninger, styrk dine løsninger, og giv dine kunder en bedre oplevelse.
            </p>
            <div className="mt-8 flex flex-wrap gap-3">
              <Button size="lg" className="shadow-blue-500/40">
                Kontakt os om partnerskab
              </Button>
              <a href="#partnerfordele" className="inline-block">
                <Button size="lg" variant="outline" className="text-white border-white/60 hover:text-white">
                  Se partnerfordele
                </Button>
              </a>
            </div>
            <div className="mt-6 flex flex-wrap items-center gap-3 text-sm text-blue-100/90">
              <span className="inline-flex items-center gap-2 rounded-full bg-white/10 px-3 py-1">
                <span className="h-2 w-2 rounded-full bg-emerald-400" /> Klar pris – ingen overraskelser
              </span>
              <span className="inline-flex items-center gap-2 rounded-full bg-white/10 px-3 py-1">
                <span className="h-2 w-2 rounded-full bg-emerald-400" /> Hjælp, når I har brug for det
              </span>
              <span className="inline-flex items-center gap-2 rounded-full bg-white/10 px-3 py-1">
                <span className="h-2 w-2 rounded-full bg-emerald-400" /> Tryg deling – hurtigt og stabilt
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Partner tiers */}
      <section id="partnerfordele" className="pt-24 pb-24 sm:pt-28 sm:pb-28 bg-gradient-to-b from-white to-blue-50/40 scroll-mt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight text-gray-900">Partnerniveauer</h2>
            <p className="mt-2 text-gray-600">
              Vælg det niveau der passer til jer. Skaler og få flere fordele, efterhånden som I vokser.
            </p>
          </div>

          <div className="mt-8 grid md:grid-cols-2 gap-6 sm:gap-8 justify-items-center">
            {/* Affiliate */}
            <Card className="w-full max-w-md border-blue-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardHeader className="text-center">
                <div className="inline-flex items-center justify-center rounded-full bg-blue-50 text-blue-700 px-3 py-1 text-xs font-medium mb-2">
                  Start her
                </div>
                <CardTitle className="text-2xl">Affiliate</CardTitle>
                <CardDescription className="text-base">
                  For creators, communities og medier, der ønsker enkel henvisning med stærke fordele.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 text-gray-700">
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <span className="mt-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                    <span>Prioriteret support</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="mt-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                    <span>Udvidede GB-grænser til deling og opbevaring</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="mt-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                    <span>Egne kampagnekoder (promo codes) til jeres community</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button size="lg" className="px-6">Ansøg som Affiliate</Button>
              </CardFooter>
            </Card>

            {/* Agency */}
            <Card className="w-full max-w-md border-blue-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardHeader className="text-center">
                <div className="inline-flex items-center justify-center rounded-full bg-indigo-50 text-indigo-700 px-3 py-1 text-xs font-medium mb-2">
                  Mest valgt
                </div>
                <CardTitle className="text-2xl">Agency</CardTitle>
                <CardDescription className="text-base">
                  Til bureauer og freelancere, der vil tilbyde FlyFiles som del af egne leverancer.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 text-gray-700">
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <span className="mt-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                    <span>Prioriteret support</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="mt-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                    <span>Udvidede GB-grænser og højere kvoter til teams</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="mt-2 h-1.5 w-1.5 rounded-full bg-emerald-500"></span>
                    <span>Opret og administrér promo codes for kunder og kampagner</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button size="lg" className="px-6">Bliv Agency-partner</Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="grid lg:grid-cols-2 gap-6 sm:gap-8">
            <Card className="border-blue-100">
              <CardHeader>
                <CardTitle>Hvorfor samarbejde med FlyFiles?</CardTitle>
                <CardDescription>
                  Fordi det skal være nemt at dele store filer – for jer og jeres kunder.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 text-gray-700">
                <ul className="list-disc pl-5 space-y-2">
                  <li>Nemt at bruge fra dag ét – ingen lange opstartsforløb</li>
                  <li>Hurtigt og stabilt – så I kan arbejde uden afbrydelser</li>
                  <li>Gennemsigtige priser – ingen småt eller ekstra gebyrer</li>
                  <li>Vi passer på data – trygt og ansvarligt</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-blue-100">
              <CardHeader>
                <CardTitle>Materialer og støtte</CardTitle>
                <CardDescription>Alt I skal bruge for at komme i gang – hurtigt og enkelt.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3 text-gray-700">
                <ul className="list-disc pl-5 space-y-2">
                  <li>Klar-til-brug billeder, tekster og skabeloner</li>
                  <li>Konkrete eksempler I kan vise til kunder</li>
                  <li>Hjælp og sparring, når I har brug for det</li>
                  <li>Mulighed for fælles historier og omtale</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>



      {/* FAQ */}
      <section className="py-4 sm:py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <h2 className="text-2xl md:text-3xl font-extrabold text-gray-900 mb-6">Ofte stillede spørgsmål</h2>
          <div className="grid md:grid-cols-2 gap-6 sm:gap-8">
            {[
              {
                q: "Hvad får vi ud af et partnerskab – helt kort?",
                a: "I får en let løsning at tilbyde jeres kunder, med prioriteret support og gode vilkår. Vi hjælper jer med materiale, idéer og en enkel måde at komme i gang på.",
              },
              {
                q: "Hvor svært er det at komme i gang?",
                a: "Det er ret ligetil. Vi tager en kort snak om jeres behov og sender alt, I skal bruge. Ingen kompliceret opsætning.",
              },
              {
                q: "Skal vi binde os til noget?",
                a: "Nej. I kan starte uforpligtende og skal kun fortsætte, hvis det giver mening for jer og jeres kunder.",
              },
              {
                q: "Hvem hjælper vi typisk?",
                a: "Bureauer, freelancere og communities der deler større filer – fx medier, fotografer, marketingteams og konsulenter.",
              },
              {
                q: "Kan vi bruge vores eget brand i kommunikationen?",
                a: "Ja. Vi har materialer, I kan sætte jeres eget præg på, så det passer til jeres stil og kunder.",
              },
              {
                q: "Hvordan kommer vi i gang?",
                a: "Skriv til os – vi aftaler en kort intro, gennemgår jeres behov og sender materialer. I kan starte uforpligtende og i jeres eget tempo.",
              }
            ].map((f) => (
                <Card
                  key={f.q}
                  className="border-blue-100"
                >
                  <CardHeader>
                    <CardTitle>{f.q}</CardTitle>
                    <CardDescription>{f.a}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <Card className="border-blue-100 shadow-sm">
            <CardContent className="p-6 md:p-10">
              <div className="grid md:grid-cols-2 gap-6 items-center">
                <div>
                  <h3 className="text-2xl md:text-3xl font-extrabold text-gray-900">
                    Klar til et uforpligtende samarbejde?
                  </h3>
                  <p className="mt-2 text-gray-700">
                    Vi tager en kort og ukompliceret snak om, hvordan vi bedst hjælper jer og jeres kunder – uden svær opsætning eller krav.
                  </p>
                </div>
                <div className="flex flex-wrap gap-3 md:justify-end justify-center">
                  <Button size="lg" className="px-6">Kontakt partnerteam</Button>
                  <Button size="lg" variant="outline" className="text-blue-700 border-blue-600 px-6">
                    Se flere detaljer
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </main>
  )
}