"use client";

import { Toaster } from "sonner";

export default function Toast() {
  return (
    <Toaster
      position="top-center"
      toastOptions={{
        duration: 3200,
        style: {
          background: 'var(--background)',
          color: 'var(--foreground)',
          border: '1px solid var(--border)',
          borderRadius: '8px',
          boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
          fontSize: '14px',
          maxWidth: '92vw',
          padding: '12px 16px',
        },
        className: "rounded-lg shadow-lg sm:max-w-xl",
      }}
      richColors
      closeButton
      theme="system"
      expand={true}
      visibleToasts={3}
    />
  );
}