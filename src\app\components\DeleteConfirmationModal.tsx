"use client";

import React, { useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/app/components/ui/button";

export interface DeleteConfirmationModalProps {
  open: boolean;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  /**
   * When true, disables any native confirm/alert usage by parents and relies solely on this modal.
   * This component does not call window.confirm at all; ensure callers do not either.
   */
  disableNativePrompt?: boolean;
}

/**
 * Accessible confirmation modal rendered in a portal.
 * - Closes on Escape and overlay click
 * - Focuses the first button on open
 * - Includes a smooth pop-in animation
 */
export default function DeleteConfirmationModal({
  open,
  title = "Er du sikker?",
  description = "Denne handling kan ikke fortrydes. Vil du slette filen?",
  confirmText = "Slet",
  cancelText = "Annuller",
  onConfirm,
  onCancel,
  disableNativePrompt = true,
}: DeleteConfirmationModalProps) {
  const cancelRef = useRef<HTMLButtonElement | null>(null);

  useEffect(() => {
    if (!open) return;

    const handleKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.preventDefault();
        onCancel();
      }
    };

    document.addEventListener("keydown", handleKey);
    // focus management
    const t = setTimeout(() => cancelRef.current?.focus(), 0);

    return () => {
      document.removeEventListener("keydown", handleKey);
      clearTimeout(t);
    };
  }, [open, onCancel]);

  // Prevent accidental native confirm dialogs if any bubbling handlers attempt them.
  // Note: This component itself never calls window.confirm.
  if (disableNativePrompt && typeof window !== "undefined") {
    // Best-effort: patch a no-op confirm for the lifetime of this render.
    // Keeps scope local and avoids global side-effects on unmount.
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _no = (msg?: string) => true;
    // Shadow the global confirm in this closure; event handlers below will use this lexical scope.
    // Do NOT assign to window.confirm globally to avoid side-effects.
    // eslint-disable-next-line no-unused-vars
    const confirm = _no;
  }

  if (typeof window === "undefined" || !open) return null;

  const modal = (
    <div
      aria-modal="true"
      role="dialog"
      aria-labelledby="delete-modal-title"
      aria-describedby="delete-modal-desc"
      className="fixed inset-0 z-[2000] flex items-center justify-center"
    >
      {/* Overlay */}
      <div
        className="ff-modal-overlay absolute inset-0 bg-black/40"
        onClick={onCancel}
      />
      {/* Panel */}
      <div className="ff-modal-panel relative mx-4 w-full max-w-md rounded-2xl border border-red-200 bg-white shadow-2xl">
        <div className="p-6">
          <h2
            id="delete-modal-title"
            className="text-lg font-semibold text-gray-900"
          >
            {title}
          </h2>
          <p id="delete-modal-desc" className="mt-2 text-sm text-gray-700">
            {description}
          </p>

          <div className="mt-6 flex items-center justify-end gap-3">
            <Button
              ref={cancelRef}
              type="button"
              variant="ghost"
              className="hover:bg-blue-50 text-gray-800"
              onClick={onCancel}
            >
              {cancelText}
            </Button>
            <Button
              type="button"
              className="bg-red-600 text-white hover:bg-red-700"
              onClick={onConfirm}
            >
              {confirmText}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modal, document.body);
}