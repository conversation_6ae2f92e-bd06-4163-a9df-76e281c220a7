export type SpotifyNowPlaying = {
  isPlaying: boolean;
  title: string | null;
  artists: string | null;
  album: string | null;
  image: string | null;
  url: string | null;
  durationMs: number | null;
  progressMs: number | null;
};

function getEnv(name: string): string | null {
  const value = process.env[name];
  return value ?? null;
}

async function getAccessToken(): Promise<string | null> {
  const clientId = getEnv('SPOTIFY_CLIENT_ID');
  const clientSecret = getEnv('SPOTIFY_CLIENT_SECRET');
  const refreshToken = getEnv('SPOTIFY_REFRESH_TOKEN');
  if (!clientId || !clientSecret || !refreshToken) return null;

  const basic = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
  const res = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${basic}`,
    },
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    }),
    cache: 'no-store',
  });

  if (!res.ok) return null;
  const json = await res.json();
  return json.access_token as string;
}

export async function fetchSpotifyCurrentlyPlaying(): Promise<SpotifyNowPlaying | null> {
  const token = await getAccessToken();
  if (!token) return null;

  const res = await fetch('https://api.spotify.com/v1/me/player/currently-playing', {
    headers: { Authorization: `Bearer ${token}` },
    cache: 'no-store',
  });

  if (res.status === 204 || res.status === 202) {
    return {
      isPlaying: false,
      title: null,
      artists: null,
      album: null,
      image: null,
      url: null,
      durationMs: null,
      progressMs: null,
    };
  }
  if (!res.ok) return null;

  const data = await res.json();
  const item = data?.item;
  const isPlaying = Boolean(data?.is_playing);
  if (!item) {
    return {
      isPlaying: false,
      title: null,
      artists: null,
      album: null,
      image: null,
      url: null,
      durationMs: null,
      progressMs: null,
    };
  }

  const image = Array.isArray(item?.album?.images) ? item.album.images[0]?.url ?? null : null;
  const artists = Array.isArray(item?.artists) ? item.artists.map((a: any) => a.name).join(', ') : null;

  return {
    isPlaying,
    title: item?.name ?? null,
    artists,
    album: item?.album?.name ?? null,
    image,
    url: item?.external_urls?.spotify ?? null,
    durationMs: typeof item?.duration_ms === 'number' ? item.duration_ms : null,
    progressMs: typeof data?.progress_ms === 'number' ? data.progress_ms : 0,
  };
}
