import { NextRequest, NextResponse } from 'next/server'
import { getFilesCollection, getSessionsCollection } from '@/app/lib/mongodb'
import { cleanupOldProgress } from '@/app/lib/upload-progress'

export async function POST(request: NextRequest) {
  try {
    console.log('Cleanup: Starting cleanup process')
    
    // Clean up expired files
    const files = await getFilesCollection()
    const now = new Date()
    
    const expiredFilesResult = await files.deleteMany({
      expiryDate: { $lt: now }
    })
    
    console.log(`Cleanup: Deleted ${expiredFilesResult.deletedCount} expired files`)
    
    // Clean up expired sessions
    const sessions = await getSessionsCollection()
    const expiredSessionsResult = await sessions.deleteMany({
      expiresAt: { $lt: now }
    })
    
    console.log(`Cleanup: Deleted ${expiredSessionsResult.deletedCount} expired sessions`)
    
    // Clean up old upload progress records
    await cleanupOldProgress()
    
    return NextResponse.json({
      success: true,
      message: 'Cleanup completed',
      deletedFiles: expiredFilesResult.deletedCount,
      deletedSessions: expiredSessionsResult.deletedCount
    })
    
  } catch (error) {
    console.error('Cleanup error:', error)
    return NextResponse.json(
      { success: false, error: 'Cleanup failed' },
      { status: 500 }
    )
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  )
} 