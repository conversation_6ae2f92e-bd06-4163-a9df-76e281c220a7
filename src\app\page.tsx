"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { useState, useRef, useCallback } from "react"
import { Button } from "./components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./components/ui/card"
import { Upload, Shield, Clock, Users, X, File } from "lucide-react"

export default function Home() {
  const { data: session } = useSession()
  const [isDragOver, setIsDragOver] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const MAX_FILE_SIZE = 250 * 1024 * 1024 // 250MB in bytes

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    handleFileSelection(files)
  }, [])

  const handleFileSelection = (files: File[]) => {
    const validFiles = files.filter(file => file.size <= MAX_FILE_SIZE)
    const oversizedFiles = files.filter(file => file.size > MAX_FILE_SIZE)

    if (oversizedFiles.length > 0) {
      alert(`${oversizedFiles.length} fil(er) er større end 250MB og kan ikke uploades som gæst.`)
    }

    setSelectedFiles(prev => [...prev, ...validFiles])
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files)
      handleFileSelection(files)
    }
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return

    setIsUploading(true)
    setUploadProgress(0)

    try {
      const uploadedFiles = []
      const totalFiles = selectedFiles.length

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i]
        const formData = new FormData()
        formData.append('file', file)

        // Update progress for current file
        setUploadProgress(Math.round((i / totalFiles) * 100))

        const response = await fetch('/api/gofile/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Upload failed')
        }

        const result = await response.json()
        uploadedFiles.push({
          name: file.name,
          downloadPage: result.data?.downloadPage,
          directLink: result.data?.directLink,
          fileId: result.data?.fileId
        })
      }

      // Complete upload
      setUploadProgress(100)
      setIsUploading(false)
      setSelectedFiles([])

      // Show success message with download links
      const linksList = uploadedFiles.map(file =>
        `${file.name}: ${file.downloadPage || file.directLink}`
      ).join('\n')

      alert(`Filer uploadet succesfuldt!\n\nDownload links:\n${linksList}`)

    } catch (error) {
      console.error('Upload error:', error)
      setIsUploading(false)
      alert(`Upload fejlede: ${error instanceof Error ? error.message : 'Ukendt fejl'}`)
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="lg:w-1/2 lg:pr-10 text-center lg:text-left mb-10 lg:mb-0">
              <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
                Danmarks nye fildelingsplatform
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight">
                Send store filer <span className="text-blue-300">sikkert</span> og nemt
              </h1>
              <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-xl mx-auto lg:mx-0">
                FlyFiles er Danmarks nye fildelingsplatform. Send filer op til 50GB med nem deling og sikker opbevaring.
              </p>
              <div className="mb-6 sm:mb-8">
                <Link
                  href="/om-os/roadmap"
                  className="group inline-flex items-center gap-2 rounded-full bg-yellow-400/20 px-4 py-2 text-yellow-100 ring-1 ring-inset ring-yellow-300/30 hover:bg-yellow-400/30 transition"
                >
                  <span className="inline-flex items-center rounded-full bg-yellow-500/30 px-2 py-0.5 text-xs font-semibold uppercase tracking-wide text-yellow-100">
                    Nyhed
                  </span>
                  <span className="text-sm">
                    FlyFiles 1.0 (beta) er officielt ude – appen kan nu bruges offentligt. Der kan stadig være fejl; følg med på vores roadmap
                  </span>
                  <span aria-hidden="true" className="ml-1 transition-transform group-hover:translate-x-0.5">→</span>
                </Link>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                {session ? (
                  <Link
                    href="/dashboard"
                    className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left"
                  >
                    <span className="relative z-10 flex items-center justify-center">
                      Gå til Dashboard
                      <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </Link>
                ) : (
                  <>
                    <Link
                      href="/dashboard"
                      className="group relative bg-white text-blue-800 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left"
                    >
                      <span className="relative z-10 flex items-center justify-center">
                        <Upload className="h-5 w-5 mr-2" />
                        Upload som gæst (250MB)
                        <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </span>
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </Link>
                    <Link href="/login" className="group relative bg-transparent border-2 border-white text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-white hover:text-blue-800 hover:shadow-2xl hover:shadow-blue-500/20 text-center sm:text-left">
                      <span className="relative z-10 flex items-center justify-center">
                        Log ind for 15GB
                        <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </span>
                    </Link>
                  </>
                )}
              </div>
            </div>

            {/* Hero Image/Animation Area */}
            <div className="lg:w-1/2 flex justify-center lg:justify-end">
              <div className="relative w-full max-w-md lg:max-w-lg">
                <div className="animate-fade-in">
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center">
                          <Upload className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">Hurtig upload</h3>
                          <p className="text-blue-200 text-sm">Op til 50GB per fil</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center">
                          <Shield className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">Sikker deling</h3>
                          <p className="text-blue-200 text-sm">Krypteret og beskyttet</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center">
                          <Clock className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">Automatisk sletning</h3>
                          <p className="text-blue-200 text-sm">Ingen permanente filer</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Guest Upload Section */}
      {!session && (
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <Card
              className={`border-2 border-dashed transition-all duration-300 ${
                isDragOver
                  ? 'border-blue-500 bg-blue-100/50 scale-105'
                  : 'border-blue-300 hover:border-blue-500 bg-blue-50/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <CardContent className="p-8">
                <div className="text-center">
                  <div className={`w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                    isDragOver ? 'scale-110 bg-blue-700' : ''
                  }`}>
                    <Upload className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">
                    {isDragOver ? 'Slip filerne her!' : 'Drag filer hertil eller klik for at uploade'}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Som gæst kan du uploade op til 250MB per fil. Alle filtyper er tilladt. Filer udløber efter 7 dage.
                  </p>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileInputChange}
                    accept="*/*"
                  />

                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="group relative bg-blue-600 text-white px-6 py-3 rounded-lg font-bold overflow-hidden transition-all duration-300 hover:bg-blue-700 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="relative z-10 flex items-center justify-center">
                      {isUploading ? 'Uploader...' : 'Vælg filer'}
                      <svg className="w-4 h-4 ml-2 transform transition-all duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                  </button>
                </div>

                {/* Selected Files Display */}
                {selectedFiles.length > 0 && (
                  <div className="mt-8 border-t pt-6">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4">Valgte filer ({selectedFiles.length})</h4>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <File className="h-5 w-5 text-blue-600" />
                            <div>
                              <p className="font-medium text-gray-800 truncate max-w-xs">{file.name}</p>
                              <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                            </div>
                          </div>
                          <button
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        </div>
                      ))}
                    </div>

                    {/* Upload Progress */}
                    {isUploading && (
                      <div className="mt-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-2">
                          <span>Uploader filer...</span>
                          <span>{uploadProgress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {/* Upload Button */}
                    {!isUploading && (
                      <button
                        onClick={handleUpload}
                        className="mt-4 w-full bg-green-600 text-white py-3 rounded-lg font-bold hover:bg-green-700 transition-colors"
                      >
                        Upload {selectedFiles.length} fil{selectedFiles.length !== 1 ? 'er' : ''}
                      </button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </section>
      )}

      {/* Why FlyFiles - Redesigned */}
      <section className="relative py-20">
        <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-blue-50/40 to-white" aria-hidden="true" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Heading */}
          <div className="text-center mb-12">
            <span className="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-3 py-1 text-xs font-medium text-blue-700">
              Trygt. Dansk. Effektivt.
            </span>
            <h2 className="mt-4 text-3xl sm:text-4xl font-extrabold tracking-tight text-gray-900">
              Hvorfor vælge <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800">FlyFiles</span>?
            </h2>
            <p className="mt-3 text-base sm:text-lg text-gray-600">
              Bygget i Danmark med fokus på sikkerhed, ydeevne og enkel deling af store filer.
            </p>
          </div>

          {/* Highlights strip */}
          <div className="mb-10 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="rounded-xl border border-blue-100 bg-white/80 backdrop-blur-sm p-4 text-center">
              <p className="text-sm text-gray-700">
                Op til <span className="font-bold text-gray-900">50GB</span> pr. fil
              </p>
            </div>
            <div className="rounded-xl border border-blue-100 bg-white/80 backdrop-blur-sm p-4 text-center">
              <p className="text-sm text-gray-700">
                Kryptering og <span className="font-bold text-gray-900">automatisk sletning</span>
              </p>
            </div>
            <div className="rounded-xl border border-blue-100 bg-white/80 backdrop-blur-sm p-4 text-center">
              <p className="text-sm text-gray-700">
                <span className="font-bold text-gray-900">Dansk</span> support og GDPR
              </p>
            </div>
          </div>

          {/* Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Security */}
            <div className="group relative h-full">
              <Card className="h-full border border-blue-100 bg-white transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-0.5">
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="grid h-12 w-12 place-items-center rounded-xl bg-blue-600 text-white transition-all duration-300 group-hover:scale-105">
                      <Shield className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">Sikker opbevaring</CardTitle>
                      <CardDescription className="text-gray-600">
                        Kryptering under overførsel og opbevaring. Privat som standard.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="mt-3 space-y-2 text-sm text-gray-600">
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      AES-256 ved opbevaring, TLS i transit
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Ingen dataanalyse eller videresalg
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Granulære delingsindstillinger
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Auto expiry */}
            <div className="group relative h-full">
              <Card className="h-full border border-blue-100 bg-white transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-0.5">
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="grid h-12 w-12 place-items-center rounded-xl bg-blue-600 text-white transition-all duration-300 group-hover:scale-105">
                      <Clock className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">Automatisk udløb</CardTitle>
                      <CardDescription className="text-gray-600">
                        Filer slettes automatisk efter din plans udløbsperiode.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="mt-3 space-y-2 text-sm text-gray-600">
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Kontrolleret levetid for alle uploads
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Midlertidig opbevaring efter udløb for sikkerhed
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Notifikationer før udløb (på vej)
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Danish support */}
            <div className="group relative h-full">
              <Card className="h-full border border-blue-100 bg-white transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-0.5">
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="grid h-12 w-12 place-items-center rounded-xl bg-blue-600 text-white transition-all duration-300 group-hover:scale-105">
                      <Users className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">Dansk platform</CardTitle>
                      <CardDescription className="text-gray-600">
                        Lokal support og fuld GDPR-compliance.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="mt-3 space-y-2 text-sm text-gray-600">
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Hurtig hjælp på dansk
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Hostet i EU
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1 h-1.5 w-1.5 rounded-full bg-blue-600" />
                      Transparent drift og regler
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* CTA */}
          <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-4">
            <Link href="/pricing">
              <button className="group relative inline-flex items-center justify-center rounded-xl bg-blue-600 px-6 py-3 font-semibold text-white shadow-sm transition-all duration-300 hover:bg-blue-700 hover:shadow-lg">
                Se planer og priser
                <span className="ml-2 transition-transform group-hover:translate-x-1" aria-hidden="true">→</span>
              </button>
            </Link>
            <span className="text-sm text-gray-600">Gratis gæsteadgang — ingen konto nødvendig</span>
          </div>
        </div>
      </section>

      {/* Pricing Teaser */}
      <section className="py-20 bg-gradient-to-r from-blue-700 to-blue-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4">
            Vælg den plan der passer til dig
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Fra gratis gæsteadgang til professionelle løsninger
          </p>
          <Link href="/pricing">
            <button className="group relative bg-white text-blue-800 px-8 py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20">
              <span className="relative z-10 flex items-center justify-center">
                Se alle priser
                <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </Link>
        </div>
      </section>
    </main>
  )
}