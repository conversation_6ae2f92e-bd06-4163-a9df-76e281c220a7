import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getFilesCollection, getUsersCollection, getSessionsCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'
import { 
  generateFileId, 
  getFileExpiryDate, 
  canUserUpload, 
  isValidFileType,
  getCurrentPeriodStart
} from '@/app/lib/utils'
import { ApiResponse, FileRecord, FileUploadRequest } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

// GET - List user's files
// IMPORTANT: This endpoint filters out expired files to ensure they never appear in the UI
// Files are filtered by both isActive status AND expiryDate to prevent timing issues
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    const userEmail = session?.user?.email || undefined
    const isAuthed = Boolean(userEmail)
    let user: any = null
    let guestSessionId: string | undefined
    if (isAuthed) {
      const users = await getUsersCollection()
      user = await users.findOne({ email: userEmail })
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' } as ApiResponse,
          { status: 404 }
        )
      }
    } else {
      guestSessionId = (request.cookies.get('guest_session_id')?.value) || undefined
      if (!guestSessionId) {
        // no files for a new guest
        return NextResponse.json({ success: true, data: [] } as ApiResponse)
      }
    }

    const url = new URL(request.url)
    const includeInactiveWithinDaysParam = url.searchParams.get('includeInactiveWithinDays')
    const includeInactiveWithinDays = includeInactiveWithinDaysParam ? parseInt(includeInactiveWithinDaysParam, 10) : undefined

    const files = await getFilesCollection()
    // Only return safe/public fields. Do NOT expose directLink, storageProvider, or storageKey.
    const projection = {
      directLink: 0,
      storageProvider: 0,
      storageKey: 0
    } as const

    const ownerFilter = isAuthed ? { ownerId: user._id.toString() } : { sessionId: guestSessionId }

    // Base: active files that haven't expired
    // This ensures expired files are never returned to the client, even if they haven't been cleaned up yet
    const now = new Date()
    const activeCursor = files
      .find(
        { 
          ...ownerFilter, 
          isActive: true,
          expiryDate: { $gt: now } // Only include files that haven't expired yet
        },
        { projection }
      )
      .sort({ uploadDate: -1 })

    const active = await activeCursor.toArray()

    // Optionally include inactive files within N days (based on expiryDate or uploadDate window)
    let inactiveRecent: any[] = []
    if (includeInactiveWithinDays && Number.isFinite(includeInactiveWithinDays) && includeInactiveWithinDays > 0) {
      const since = new Date(now.getTime() - includeInactiveWithinDays * 24 * 60 * 60 * 1000)

      // Consider recently deactivated as: isActive=false AND (uploadDate >= since OR expiryDate >= since)
      // AND still not expired - this prevents showing expired files even in the inactive list
      inactiveRecent = await files
        .find(
          {
            ...ownerFilter,
            isActive: false,
            expiryDate: { $gt: now }, // Only include non-expired files
            $or: [
              { uploadDate: { $gte: since } },
              { expiryDate: { $gte: since } }
            ]
          },
          { projection }
        )
        .sort({ uploadDate: -1 })
        .toArray()
    }

    const merged = includeInactiveWithinDays ? [...active, ...inactiveRecent] : active

    return NextResponse.json({
      success: true,
      data: merged as unknown as Omit<FileRecord, 'directLink' | 'storageProvider' | 'storageKey'>[]
    } as ApiResponse)

  } catch (error) {
    console.error('Error getting files:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

// POST - Create new file record (metadata only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    const userEmail = session?.user?.email || undefined
    
    if (!userEmail) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    const body: FileUploadRequest = await request.json()
    
    // Validate request
    if (!body.filename || !body.size || !body.mimeType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' } as ApiResponse,
        { status: 400 }
      )
    }

    // Validate file type
    if (!isValidFileType(body.mimeType)) {
      return NextResponse.json(
        { success: false, error: 'File type not allowed' } as ApiResponse,
        { status: 400 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: userEmail })
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check upload limits
    const planConfig = PLAN_CONFIGS[user.plan as keyof typeof PLAN_CONFIGS]
    const currentUsage = user.plan === 'free' ? user.usage.monthly : user.usage.weekly
    const uploadCheck = canUserUpload(user.plan, currentUsage, body.size)
    
    if (!uploadCheck.canUpload) {
      return NextResponse.json(
        { success: false, error: uploadCheck.reason } as ApiResponse,
        { status: 400 }
      )
    }

    // Create file record
    const fileId = generateFileId()
    const expiryDate = getFileExpiryDate(user.plan)
    
    const fileRecord: Omit<FileRecord, '_id'> = {
      filename: fileId,
      originalName: body.filename,
      mimeType: body.mimeType,
      size: body.size,
      ownerId: user._id.toString(),
      uploadDate: new Date(),
      expiryDate: expiryDate,
      downloadCount: 0,
      downloadLimit: body.downloadLimit || (planConfig.downloadLimits.unlimited ? -1 : 10),
      isActive: true
    }

    const files = await getFilesCollection()
    const result = await files.insertOne(fileRecord)

    // Update user usage
    const usageField = user.plan === 'free' ? 'usage.monthly' : 'usage.weekly'
    await users.updateOne(
      { _id: user._id },
      { $inc: { [usageField]: body.size } }
    )

    const createdFile = await files.findOne(
      { _id: result.insertedId },
      {
        // Do not expose sensitive storage fields
        projection: { directLink: 0, storageProvider: 0, storageKey: 0 }
      }
    )

    return NextResponse.json({
      success: true,
      data: createdFile as unknown as Omit<FileRecord, 'directLink' | 'storageProvider' | 'storageKey'>,
      message: 'File metadata created successfully'
    } as ApiResponse)

  } catch (error) {
    console.error('Error creating file:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
} 