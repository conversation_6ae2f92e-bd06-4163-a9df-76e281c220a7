@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

/* Enable smooth scrolling for in-page anchor links */
html:focus-within {
  scroll-behavior: smooth;
}
/* Fallback for general usage */
html {
  scroll-behavior: smooth;
}

/* Arrow animation for links */
.arrow-wrapper {
  display: inline-flex;
  align-items: center;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper {
  transform: translateX(8px);
}

.arrow-wrapper svg {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper svg {
  transform: scale(1.1);
}

/* 3D Cube Animation */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.cube {
  transition: transform 1s ease;
}

.scene:hover .cube-rotate {
  transform: rotateY(180deg) rotateX(45deg);
}

.cube-face {
  backface-visibility: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.cube-face-front {
  transform: translateZ(128px);
}

.cube-face-back {
  transform: rotateY(180deg) translateZ(128px);
}

.cube-face-right {
  transform: rotateY(90deg) translateZ(128px);
}

.cube-face-left {
  transform: rotateY(-90deg) translateZ(128px);
}

.cube-face-top {
  transform: rotateX(90deg) translateZ(128px);
}

.cube-face-bottom {
  transform: rotateX(-90deg) translateZ(128px);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Server Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes countUp {
  0% { content: "0"; }
  20% { content: "1"; }
  40% { content: "3"; }
  60% { content: "5"; }
  100% { content: "6"; }
}

.animate-[countUp_1s_ease-in-out_forwards]::after {
  content: "0";
  animation: countUp 1s ease-in-out forwards;
}

/* Calendar specific animations */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: scaleIn 0.2s ease-out forwards;
}

/* Hover animations */
.transition-transform {
  transition: transform 0.3s ease;
}

.hover\:transform:hover {
  transform: scale(1.05);
}

/* Form styles */
input, textarea, select {
  color: #333 !important;
  background-color: white !important;
}

input::placeholder, textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

select {
  appearance: menulist !important;
  padding-right: 2rem !important;
}

select option {
  color: #333 !important;
  background-color: white !important;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

.animate-typing {
  animation: typing 8s steps(40, end) infinite;
}

/* DeleteConfirmationModal animations (composited: transform + opacity) */
@keyframes ffModalOverlayIn {
  from { opacity: 0; }
  to   { opacity: 1; }
}
@keyframes ffModalIn {
  0%   { opacity: 0; transform: translateY(12px) scale(0.96); }
  60%  { opacity: 1; transform: translateY(0)    scale(1.01); }
  100% { opacity: 1; transform: translateY(0)    scale(1); }
}

/* Apply animations using utility classes to keep will-change only while animating */
.ff-modal-overlay {
  animation: ffModalOverlayIn 180ms ease-out both;
}

.ff-modal-panel {
  animation: ffModalIn 200ms cubic-bezier(0.16, 1, 0.3, 1) both;
  transform-origin: center;
  will-change: transform, opacity;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .ff-modal-overlay,
  .ff-modal-panel {
    animation: none !important;
    transform: none !important;
    opacity: 1 !important;
    will-change: auto !important;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
