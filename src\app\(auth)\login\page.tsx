import { redirect } from "next/navigation"
import { auth } from "@/app/lib/auth"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import Link from "next/link"
import { signIn } from "@/app/lib/auth"

export default async function LoginPage() {
  // Server-side session check; redirect if authenticated
  const session = await auth()
  if (session) {
    redirect("/dashboard")
  }

  return (
    <section
      aria-labelledby="login-title"
      className="relative pt-28 pb-16 bg-white"
    >
      {/* Subtle gradient background stripe to match branding */}
      <div className="pointer-events-none absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-blue-50 to-transparent"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="mx-auto h-16 w-16 rounded-lg flex items-center justify-center shadow-lg shadow-blue-500/30">
            <img
              src="/images/flyfiles.png"
              alt="FlyFiles logo"
              className="h-16 w-16 object-contain"
              loading="eager"
              decoding="async"
            />
          </div>
          <h1 id="login-title" className="mt-6 text-3xl sm:text-4xl font-extrabold tracking-tight text-gray-900">
            Log ind på FlyFiles
          </h1>
          <p className="mt-2 text-sm sm:text-base text-gray-600">
            Få adgang til 15GB månedlig upload, længere opbevaring og flere funktioner
          </p>
        </div>

        {/* Content */}
        <div className="mx-auto max-w-xl">
          <Card className="overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100">
              <CardTitle className="text-gray-900">Vælg login metode</CardTitle>
              <CardDescription className="text-gray-600">
                Vi bruger Google OAuth for sikker og nem adgang
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <form
                action={async () => {
                  "use server"
                  await signIn("google")
                }}
              >
                <Button
                  type="submit"
                  className="w-full flex items-center justify-center space-x-2"
                  size="lg"
                >
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3c/Google_Favicon_2025.svg/120px-Google_Favicon_2025.svg.png"
                    alt="Google logo"
                    className="h-5 w-5"
                  />
                  <span>Fortsæt med Google</span>
                </Button>
              </form>

              <div className="space-y-3 text-xs text-gray-600">
                <p className="text-gray-700">
                  Hvorfor kun Google-login?
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Bedre sikkerhed via Google’s verificerede OAuth-infrastruktur</li>
                  <li>Mindre konto-vedligeholdelse og færre adgangskodeproblemer</li>
                  <li>Hurtigere onboarding, så vi kan fokusere på produktets kerne</li>
                </ul>
                <p className="text-gray-500">
                  Vi planlægger at tilføje flere login-muligheder (fx e-mail og Microsoft) senere.
                </p>
              </div>

              <p className="text-center text-xs text-gray-500">
                Ved at logge ind accepterer du vores{" "}
                <Link href="/tos" className="text-blue-600 hover:text-blue-700 underline underline-offset-2">
                  Servicevilkår
                </Link>{" "}
                og{" "}
                <Link href="/privatlivspolitik" className="text-blue-600 hover:text-blue-700 underline underline-offset-2">
                  Privatlivspolitik
                </Link>
                .
              </p>
            </CardContent>
          </Card>

          {/* Benefits */}
          <Card className="mt-6">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-3 text-gray-900">Fordele ved at logge ind</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 15GB månedlig upload (vs. 250MB som gæst)</li>
                <li>• 10 dages fil-opbevaring (vs. 7 dage)</li>
                <li>• Konfigurerbare download-grænser</li>
                <li>• Filhistorik og statistikker</li>
                <li>• Mulighed for upgrade til større planer</li>
              </ul>
            </CardContent>
          </Card>

          {/* Back link */}
          <div className="text-center mt-6">
            <Link
              href="/"
              className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700"
            >
              <svg className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
              </svg>
              Tilbage til forsiden
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}