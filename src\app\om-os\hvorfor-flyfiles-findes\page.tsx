import Link from "next/link";

export const metadata = {
  title: "Hvorfor FlyFiles findes | FlyFiles",
  description:
    "Den fulde historie: <PERSON> ('MyckasP') så en trend mod databrug uden tydeligt samtykke, besluttede han at bygge en enkel, billig og brugervenlig fildelingstjeneste – uden at udnytte dine data. <PERSON>æs hvorfor og hvordan.",
};

export default function HvorforFlyFilesFindesPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-700 via-blue-800 to-blue-900 text-white py-16 sm:py-20 md:py-28">
        <div className="absolute inset-0 opacity-20 pointer-events-none">
          <div className="absolute -top-24 -left-16 h-72 w-72 rounded-full bg-blue-400 blur-3xl" />
          <div className="absolute top-1/3 -right-10 h-72 w-72 rounded-full bg-cyan-400 blur-3xl" />
          <div className="absolute bottom-0 left-1/4 h-56 w-56 rounded-full bg-indigo-500 blur-3xl" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6">
          <div className="max-w-3xl">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm text-blue-50 text-sm font-medium">
              Om os
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold tracking-tight">
              Hvorfor <span className="text-blue-200">FlyFiles</span> findes
            </h1>
            <p className="mt-4 text-blue-100 text-lg md:text-xl">
              En gennemarbejdet fortælling om det øjeblik, der udløste idéen, bekymringen bag, og planen om at bygge et bedre, enklere og billigere alternativ – uden at bruge dine data.
            </p>
          </div>
        </div>
      </section>

      {/* Story */}
      <section className="py-12 sm:py-16 bg-gradient-to-b from-white to-blue-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="mx-auto max-w-3xl">
            <article className="bg-white rounded-2xl border border-blue-100 shadow-sm p-6 sm:p-8">
              {/* Intro link to Om FlyFiles / Mickas */}
              <div className="mb-6">
                <p className="text-gray-700 leading-relaxed">
                  Vil du først læse om personen bag? Mød{" "}
                  <Link
                    href="/om-os/om-flyfiles"
                    className="underline decoration-blue-300 underline-offset-4 text-blue-700 hover:text-blue-800 hover:decoration-blue-400 transition-colors font-medium"
                  >
                    “Mickas” aka “MyckasP”
                  </Link>{" "}
                  og baggrunden for FlyFiles.
                </p>
              </div>

              <h2 className="text-2xl md:text-3xl font-extrabold tracking-tight text-gray-900">
                Det øjeblik, der ændrede alt
              </h2>
              <p className="mt-3 text-gray-700 leading-relaxed">
                En dag brugte Mickas – også kendt som <span className="font-semibold">“MyckasP”</span> – en populær fildelingstjeneste. Midt i hverdagsrutinen bemærkede han noget mærkeligt: deres vilkår og privatlivspolitik blev ændret, og kort efter justeret igen. I en periode stod der, at de ville bruge{" "}
                <span className="font-semibold">maskinlæring</span> – med implikationer for, hvordan brugernes filer kunne indgå i træning af systemer.
              </p>
              <p className="mt-3 text-gray-700 leading-relaxed">
                Den efterfølgende diskussion og tilbagetrækning er bl.a. beskrevet her:{" "}
                <a
                  href="https://www.malwarebytes.com/blog/news/2025/07/wetransfer-walks-back-clause-that-said-it-would-train-ai-on-your-files"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 underline decoration-blue-300 underline-offset-4 text-blue-700 hover:text-blue-800 hover:decoration-blue-400 transition-colors font-medium"
                >
                  WeTransfer walks back clause that said it would train AI on your files
                  <svg
                    className="h-4 w-4 opacity-80"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path d="M12.5 2a.75.75 0 0 0 0 1.5h2.69l-6.72 6.72a.75.75 0 1 0 1.06 1.06l6.72-6.72v2.69A.75.75 0 0 0 17.75 7V2.75A.75.75 0 0 0 17 2h-4.5z" />
                    <path d="M5.5 4A1.5 1.5 0 0 0 4 5.5v9A1.5 1.5 0 0 0 5.5 16h9a1.5 1.5 0 0 0 1.5-1.5V10a.75.75 0 0 0-1.5 0v4.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5H10a.75.75 0 0 0 0-1.5H5.5z" />
                  </svg>
                </a>
                .
              </p>

              <h3 className="mt-8 text-xl md:text-2xl font-bold text-gray-900">
                Bekymringen: samtykke, data og tillid
              </h3>
              <p className="mt-3 text-gray-700 leading-relaxed">
                Den slags ændringer – uanset om de bliver rullet tilbage – skaber usikkerhed. Mange er bekymrede for, at deres data kan blive brugt på måder, de ikke reelt har givet informeret samtykke til. Selv små formuleringer i vilkår kan få store konsekvenser for, hvordan indhold må analyseres, gemmes og anvendes.
              </p>
              <p className="mt-3 text-gray-700 leading-relaxed">
                For Mickas stod det klart: Der er behov for en løsning, der prioriterer enkelhed, pris og respekt for brugernes data. Ikke bare i kommunikation – men i selve produktets design og drift.
              </p>

              <h3 className="mt-8 text-xl md:text-2xl font-bold text-gray-900">
                Planen: et bedre alternativ til fildeling
              </h3>
              <ul className="mt-3 space-y-2 text-gray-700 list-disc pl-5">
                <li>
                  <span className="font-semibold">Enkelt</span> – det skal være lige så nemt som at sende et link.
                </li>
                <li>
                  <span className="font-semibold">Billigt</span> – fair og gennemsigtige priser uden at låse basisfunktioner bag dyre vægge.
                </li>
                <li>
                  <span className="font-semibold">Brugervenligt</span> – både for afsender og modtager, uden konto-krav for at hente.
                </li>
                <li>
                  <span className="font-semibold">Ingen udnyttelse af dine data</span> – ingen brug af dine filer til træning af modeller. Punktum.
                </li>
              </ul>

              <h3 className="mt-8 text-xl md:text-2xl font-bold text-gray-900">
                Principper vi bygger efter
              </h3>
              <div className="mt-3 grid gap-4 sm:grid-cols-2">
                <div className="rounded-xl border border-blue-100 bg-blue-50/40 p-4">
                  <h4 className="font-semibold text-gray-900">Gennemsigtighed</h4>
                  <p className="mt-1 text-gray-700">
                    Klare vilkår, klare funktioner, klare priser. Ingen skjulte formuleringer eller overraskelser.
                  </p>
                </div>
                <div className="rounded-xl border border-blue-100 bg-blue-50/40 p-4">
                  <h4 className="font-semibold text-gray-900">Dataminimalisme</h4>
                  <p className="mt-1 text-gray-700">
                    Vi samler kun det nødvendige for at levere tjenesten – ikke for at udnytte eller profilere dig.
                  </p>
                </div>
                <div className="rounded-xl border border-blue-100 bg-blue-50/40 p-4">
                  <h4 className="font-semibold text-gray-900">Ydelse og stabilitet</h4>
                  <p className="mt-1 text-gray-700">
                    Fokus på stabile uploads, hastighed og tydelig feedback. Teknik for mennesker – ikke omvendt.
                  </p>
                </div>
                <div className="rounded-xl border border-blue-100 bg-blue-50/40 p-4">
                  <h4 className="font-semibold text-gray-900">Feedback-drevet</h4>
                  <p className="mt-1 text-gray-700">
                    Vi lytter, måler og forbedrer løbende ud fra virkelige behov – ikke hype.
                  </p>
                </div>
              </div>

              <h3 className="mt-8 text-xl md:text-2xl font-bold text-gray-900">
                Status: Beta i dag – stabil meget snart
              </h3>
              <p className="mt-3 text-gray-700 leading-relaxed">
                FlyFiles er lige nu i <span className="font-semibold">beta</span> og bygger delvist på beta-funktioner og -løsninger. Det betyder, at vi bevæger os hurtigt og tester i virkeligheden – men med høj disciplin omkring sikkerhed og databeskyttelse. Stabil udgave er på vej meget snart.
              </p>

              <div className="mt-8 p-5 sm:p-6 rounded-xl border border-blue-100 bg-blue-50/40">
                <h3 className="text-xl md:text-2xl font-bold text-gray-900">
                  Hvad betyder det for dig?
                </h3>
                <p className="mt-2 text-gray-700 leading-relaxed">
                  Uanset om du er freelancer, studerende eller en mindre virksomhed, skal fildeling være hurtigt, ukompliceret og rimeligt prissat – uden at du skal betale med dine data. Det er hele pointen med FlyFiles.
                </p>
                <div className="mt-4 flex flex-wrap gap-3">
                  <Link
                    href="/"
                    className="inline-flex items-center justify-center h-10 px-5 rounded-md bg-blue-700 text-white hover:bg-blue-800 transition-colors"
                  >
                    Upload som gæst
                  </Link>
                  <Link
                    href="/pricing"
                    className="inline-flex items-center justify-center h-10 px-5 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
                  >
                    Se priser
                  </Link>
                  <Link
                    href="/om-os/om-flyfiles"
                    className="inline-flex items-center justify-center h-10 px-5 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
                  >
                    Læs om “Mickas” / “MyckasP”
                  </Link>
                  <Link
                    href="/om-os/roadmap"
                    className="inline-flex items-center justify-center h-10 px-5 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
                  >
                    Se vores roadmap
                  </Link>
                </div>
              </div>
            </article>

            <div className="mt-8 flex flex-wrap gap-3">
              <Link
                href="/om-os/om-flyfiles"
                className="inline-flex items-center justify-center h-10 px-5 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
              >
                Tilbage til Om FlyFiles
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}