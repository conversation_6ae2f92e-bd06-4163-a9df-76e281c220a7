import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getSupportRequestsCollection, getUsersCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'

type CreateSupportBody = {
  name: string
  email: string
  subject: string
  subjectDetail?: string
  contactMethod: 'email' | 'here'
  message: string
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    const coll = await getSupportRequestsCollection()
    const items = await coll
      .find({ userId: user._id.toString() })
      .sort({ createdAt: -1 })
      .limit(50)
      .toArray()

    return NextResponse.json({
      success: true,
      data: items
    } as ApiResponse)
  } catch (err) {
    console.error('GET /api/support error:', err)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as CreateSupportBody

    if (!body?.name || !body?.email || !body?.subject || !body?.message || !body?.contactMethod) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' } as ApiResponse,
        { status: 400 }
      )
    }

    const session = await auth()

    // If user chose "here", they must be authenticated
    if (body.contactMethod === 'here') {
      if (!session?.user?.email) {
        return NextResponse.json(
          { success: false, error: 'Login required for on-site replies' } as ApiResponse,
          { status: 401 }
        )
      }
    }

    // If authenticated, attach userId. If not, store as guest (userId undefined)
    let userId: string | undefined
    if (session?.user?.email) {
      const users = await getUsersCollection()
      const user = await users.findOne({ email: session.user.email })
      if (user) {
        userId = user._id.toString()
      }
    }

    const coll = await getSupportRequestsCollection()
    const doc = {
      userId,
      name: body.name,
      email: body.email,
      subject: body.subject,
      subjectDetail: body.subjectDetail || '',
      contactMethod: body.contactMethod,
      message: body.message,
      status: 'open' as const,
      replies: [] as { message: string; createdAt: Date; by: 'user' | 'support' }[],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = await coll.insertOne(doc as any)
    const created = await coll.findOne({ _id: result.insertedId })

    return NextResponse.json({
      success: true,
      data: created,
      message: 'Support request created'
    } as ApiResponse)
  } catch (err) {
    console.error('POST /api/support error:', err)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}