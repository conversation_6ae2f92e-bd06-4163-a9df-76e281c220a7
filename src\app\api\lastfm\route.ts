import { NextResponse } from 'next/server';
import { fetchLastFmNowPlaying } from '@/app/lib/lastfm';
import { fetchSpotifyCurrentlyPlaying } from '@/app/lib/spotify';

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Prefer Spotify only when actually playing to get progress/duration.
    // Otherwise, fall back to Last.fm to show the last played track.
    const spotify = await fetchSpotifyCurrentlyPlaying();
    if (spotify && spotify.isPlaying) {
      return NextResponse.json(
        {
          source: 'spotify',
          ...spotify,
        },
        { headers: { 'Cache-Control': 's-maxage=15, stale-while-revalidate=120' } }
      );
    }

    const lastfm = await fetchLastFmNowPlaying();
    // If Last.fm has no data for some reason, fall back to Spotify payload (even if paused)
    const payload = (lastfm && (lastfm.title || lastfm.image || lastfm.url))
      ? { source: 'lastfm' as const, ...lastfm }
      : { source: 'spotify' as const, ...(spotify ?? {}) };

    return NextResponse.json(payload, {
      headers: {
        'Cache-Control': 's-maxage=30, stale-while-revalidate=300',
      },
    });
  } catch (error) {
    // Hide details from client; log on server if needed
    return NextResponse.json(
      {
        source: 'lastfm',
        isPlaying: false,
        title: null,
        artists: null,
        album: null,
        image: null,
        url: null,
        playedAt: null,
        durationMs: null,
      },
      { status: 200 }
    );
  }
}
