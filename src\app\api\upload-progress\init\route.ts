import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { updateUploadProgress } from '@/app/lib/upload-progress'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    const hasGuest = Boolean(request.cookies.get('guest_session_id'))
    if (!session?.user?.email && !hasGuest) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    const { uploadId } = await request.json()

    if (!uploadId) {
      return NextResponse.json({ success: false, error: 'Missing uploadId' }, { status: 400 })
    }

    // Initialize progress tracking
    await updateUploadProgress(uploadId, 0, 'starting')

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to initialize progress' },
      { status: 500 }
    )
  }
} 