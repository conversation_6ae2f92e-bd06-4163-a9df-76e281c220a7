import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getFilesCollection } from '@/app/lib/mongodb'
import { type FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'
import { CopyLinkButton } from '@/app/d/[fileId]/copy-link-client'
import DownloadButton from '@/app/d/[fileId]/DownloadButton'

export default async function DownloadPage({
  params,
}: {
  params: Promise<{ fileId: string }>
}) {
  const { fileId } = await params
  const files = await getFilesCollection()

  let file = (await files.findOne({ filename: fileId, isActive: true })) as unknown as FileRecord | null

  if (!file && ObjectId.isValid(fileId)) {
    const byId = (await files.findOne({ _id: new ObjectId(fileId), isActive: true })) as unknown as FileRecord | null
    if (byId) file = byId
  }

  if (!file) {
    notFound()
  }

  // Check if file is expired
  const isExpired = new Date() > new Date(file.expiryDate)
  
  // Check if download limit is reached
  const limitReached = file.downloadLimit !== -1 && file.downloadCount >= file.downloadLimit

  const sizeFormatter = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('da-DK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const base = process.env.NEXT_PUBLIC_BASE_URL || ''
  const pageUrl = `${base}/d/${file!.filename}`

  return (
    <main className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <section className="relative py-12 sm:py-16">
        <div className="mx-auto max-w-3xl px-4 sm:px-6">
          <div className="overflow-hidden rounded-2xl border border-blue-100 bg-white shadow-xl shadow-blue-100/50">
            <div className="relative bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 sm:px-8">
              <div className="flex items-start gap-4">
                <div className="rounded-xl bg-white/10 p-3 text-white">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <path d="M12 3v14m0 0l-5-5m5 5l5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">{file!.originalName}</h1>
                  <p className="text-sm text-blue-100">{file!.mimeType} • {sizeFormatter(file!.size)}</p>
                </div>
              </div>
            </div>

            <div className="px-6 py-8 sm:px-8">
              <div className="space-y-6">
                {isExpired ? (
                  <div className="rounded-lg bg-red-50 p-4 text-center">
                    <h2 className="text-lg font-bold text-red-800">Fil udløbet</h2>
                    <p className="text-red-700">
                      Denne fil udløb den {formatDate(new Date(file.expiryDate))} og er ikke længere tilgængelig for download.
                    </p>
                  </div>
                ) : limitReached ? (
                  <div className="rounded-lg bg-yellow-50 p-4 text-center">
                    <h2 className="text-lg font-bold text-yellow-800">Download grænse nået</h2>
                    <p className="text-yellow-700">
                      Denne fil har nået sin download grænse på {file.downloadLimit} downloads.
                    </p>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="mb-6">
                      <DownloadButton 
                        expired={isExpired || limitReached}
                        fileId={file.filename}
                        filename={file.originalName}
                      />
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      Klik på knappen ovenfor for at downloade filen direkte til din enhed.
                    </p>
                    <p className="text-xs text-gray-500">
                      Sikker download • Ingen registrering krævet
                    </p>
                  </div>
                )}

                <div className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-900">Filstørrelse</div>
                      <div className="text-gray-600">{sizeFormatter(file.size)}</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-900">Filtype</div>
                      <div className="text-gray-600">{file.mimeType}</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-900">Uploadet</div>
                      <div className="text-gray-600">{formatDate(new Date(file.uploadDate))}</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-900">Udløber</div>
                      <div className="text-gray-600">{formatDate(new Date(file.expiryDate))}</div>
                    </div>
                    {file.downloadLimit !== -1 && (
                      <div className="bg-gray-50 rounded-lg p-3 sm:col-span-2">
                        <div className="font-medium text-gray-900">Downloads</div>
                        <div className="text-gray-600">
                          {file.downloadCount} af {file.downloadLimit} downloads brugt
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex justify-center">
                    <CopyLinkButton url={pageUrl} />
                  </div>
                </div>
              </div>

              <div className="mt-8 rounded-xl border border-gray-100 bg-gray-50 px-4 py-3 text-xs text-gray-600">
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <div className="flex items-center gap-2">
                    <span className="inline-flex h-2 w-2 rounded-full bg-green-500"></span>
                    Sikker og pålidelig fildelingstjeneste
                  </div>
                  <Link href="/" className="text-blue-700 hover:underline">
                    Til forsiden
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}