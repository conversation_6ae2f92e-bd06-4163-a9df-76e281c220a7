"use client"

import { useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"

type Invite = {
  id: string
  email: string
  role: "admin" | "member" | "viewer"
  status: "pending" | "accepted" | "expired"
  invitedAt: string
}

type Member = {
  id: string
  name: string | null
  email: string | null
  role: "owner" | "admin" | "member" | "viewer"
  joinedAt: string
}

const skeleton = "animate-pulse bg-gray-200 rounded-md"

export default function TeamPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Redirect unauthenticated users to login
  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/login")
  }, [status, session, router])

  if (status === "loading") {
    return (
      <main className="min-h-[70vh] flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </main>
    )
  }

  if (!session) {
    return null
  }

  // Safely read accountType with fallback to "individual"
  const accountType = ((session.user as any)?.accountType as string) || "individual"
  const isTeam = accountType === "team"

  // Data sources (no local demo); plain constants (not hooks) to avoid hook order changes
  const members: Member[] = []
  const invites: Invite[] = []


  const roleBadge = (role: Member["role"] | Invite["role"]) => {
    const base = "px-2 py-0.5 rounded-full text-xs font-semibold"
    switch (role) {
      case "owner":
        return <span className={`${base} bg-yellow-100 text-yellow-800`}>Owner</span>
      case "admin":
        return <span className={`${base} bg-purple-100 text-purple-800`}>Admin</span>
      case "member":
        return <span className={`${base} bg-blue-100 text-blue-800`}>Member</span>
      case "viewer":
        return <span className={`${base} bg-gray-100 text-gray-800`}>Viewer</span>
    }
  }

  const statusBadge = (status: Invite["status"]) => {
    const base = "px-2 py-0.5 rounded-full text-xs font-semibold"
    switch (status) {
      case "pending":
        return <span className={`${base} bg-amber-100 text-amber-800`}>Afventer</span>
      case "accepted":
        return <span className={`${base} bg-emerald-100 text-emerald-800`}>Accepteret</span>
      case "expired":
        return <span className={`${base} bg-rose-100 text-rose-800`}>Udløbet</span>
    }
  }

  const canInvite = isTeam
  const teamSize = members.length
  const pendingCount = invites.filter(i => i.status === "pending").length

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Header */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-12 sm:py-16 overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 relative">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
            <div>
              <div className="inline-flex items-center justify-center mb-3 px-4 py-1.5 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
                Kontotype: {isTeam ? "Team" : "Individuel"}
              </div>
              <h1 className="text-3xl sm:text-4xl font-extrabold mb-2 leading-tight">Teamadministration</h1>
              <p className="text-blue-100 max-w-2xl">
                {isTeam
                  ? "Administrér medlemmer, invitationer og roller. Flere avancerede indstillinger kommer snart."
                  : "Din konto er individuel. Opgrader til team for at invitere medlemmer og samarbejde bedre."}
              </p>
            </div>

            <div className="flex flex-wrap gap-3">
              <Link href="/dashboard">
                <Button variant="outline" className="border-white/70 text-white hover:bg-white/10">
                  Til dashboard
                </Button>
              </Link>
              {!isTeam && (
                <Link href="/pricing">
                  <Button variant="outline" className="border-white/70 text-white hover:bg-white/10">
                    Se team-muligheder
                  </Button>
                </Link>
              )}
            </div>
          </div>

          {/* Quick stats */}
          {isTeam && (
            <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4">
              <StatCard label="Medlemmer" value={`${teamSize}`} hint="inkl. ejer" />
              <StatCard label="Afventende invitationer" value={`${pendingCount}`} hint="kan annulleres" />
              <StatCard label="Rolle-matrix" value="Snart" hint="adgangsniveauer" soon />
            </div>
          )}
        </div>
      </section>

      {/* Content */}
      <section className="py-10 sm:py-14">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Account summary */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle>Kontooversigt</CardTitle>
                <CardDescription>Grundlæggende oplysninger om din konto</CardDescription>
              </CardHeader>
              <CardContent className="text-sm text-gray-700 space-y-3">
                <KeyValue label="E-mail" value={session.user?.email || "—"} />
                <KeyValue label="Navn" value={session.user?.name || "—"} />
                <KeyValue label="Kontotype" value={isTeam ? "Team" : "Individuel"} />
              </CardContent>
              {!isTeam && (
                <CardFooter className="pt-0">
                  <Link href="/pricing">
                    <Button className="w-full">Opgrader til Team</Button>
                  </Link>
                </CardFooter>
              )}
            </Card>

            {/* Team panel */}
            <Card className="lg:col-span-2">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between gap-4">
                  <div>
                    <CardTitle>{isTeam ? "Teammedlemmer" : "Opgrader til Team"}</CardTitle>
                    <CardDescription>
                      {isTeam
                        ? "Inviter og administrér teammedlemmer. Roller og avancerede indstillinger kommer snart."
                        : "Få adgang til teamfunktioner som invitationer, roller og fælles brug."}
                    </CardDescription>
                  </div>
                  {isTeam && (
                    <div className="flex gap-2">
                      <Button size="sm" disabled variant="secondary" title="Kommer snart">
                        Inviter medlem
                      </Button>
                      <Button size="sm" disabled variant="outline" title="Kommer snart">
                        Rolleindstillinger
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {isTeam ? (
                  <div className="space-y-6">
                    {/* Members list */}
                    <div>
                      <EmptyState
                        title="Ingen medlemmer endnu"
                        description="Invitér dine kollegaer for at begynde at samarbejde."
                        cta={<Button disabled>Inviter medlem (snart)</Button>}
                      />
                    </div>

                    {/* Invites list */}
                    <div>
                      <h3 className="text-sm font-bold text-gray-800 mb-2">Invitationer</h3>
                      <EmptyState
                        compact
                        title="Ingen afventende invitationer"
                        description="Når du inviterer et medlem, vises det her."
                      />
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-700">
                    <ul className="list-disc pl-5 space-y-2 mb-5">
                      <li>Invitér medlemmer til dit team</li>
                      <li>Fastlæg roller og adgang</li>
                      <li>Fælles usage og fakturering</li>
                    </ul>
                    <div className="flex flex-wrap gap-3">
                      <Link href="/pricing">
                        <Button className="bg-blue-600 hover:bg-blue-700">Se team-planer</Button>
                      </Link>
                      <Link href="/support">
                        <Button variant="outline">Spørg support</Button>
                      </Link>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Roadmap / Help */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Rolle- og adgangsstyring</CardTitle>
                <CardDescription>Kommer snart</CardDescription>
              </CardHeader>
              <CardContent className="text-sm text-gray-700">
                Vi arbejder på en fleksibel rollemodel (Owner, Admin, Member, Viewer) med granulære rettigheder.
                Følg med på vores roadmap.
              </CardContent>
              <CardFooter>
                <Link href="/om-os/roadmap">
                  <Button variant="link">Se roadmap →</Button>
                </Link>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Brug for hjælp?</CardTitle>
                <CardDescription>Kontakt supporten</CardDescription>
              </CardHeader>
              <CardContent className="text-sm text-gray-700">
                Har du spørgsmål til teamfunktioner eller enterprise-behov, så ræk ud. Vi hjælper gerne med opsætning.
              </CardContent>
              <CardFooter className="gap-3">
                <Link href="/support">
                  <Button>Kontakt support</Button>
                </Link>
                <Link href="/pricing">
                  <Button variant="outline">Enterprise</Button>
                </Link>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>
    </main>
  )
}

/* Utilities & presentational components kept local for now */

function KeyValue({ label, value }: { label: string; value: string }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-gray-600">{label}</span>
      <span className="font-semibold text-gray-900">{value}</span>
    </div>
  )
}

function StatCard({
  label,
  value,
  hint,
  soon = false,
}: {
  label: string
  value: string
  hint?: string
  soon?: boolean
}) {
  return (
    <div className="rounded-xl border border-blue-100 bg-white/90 backdrop-blur-sm p-4 shadow-lg shadow-blue-50/50">
      <div className="text-xs text-blue-700/80 font-semibold">{label}</div>
      <div className="mt-1 text-2xl font-extrabold text-gray-900">{value}</div>
      <div className="text-xs text-gray-500">{hint}</div>
      {soon && <div className="mt-2 inline-block text-[10px] px-2 py-0.5 rounded-full bg-gray-100 text-gray-700">Snart</div>}
    </div>
  )
}

function EmptyState({
  title,
  description,
  cta,
  compact = false,
}: {
  title: string
  description?: string
  cta?: React.ReactNode
  compact?: boolean
}) {
  return (
    <div className="rounded-lg border border-dashed border-blue-200 p-6 text-center bg-gradient-to-b from-white to-blue-50/30">
      <div className="mx-auto mb-2 h-10 w-10 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center font-extrabold">
        +
      </div>
      <div className="font-semibold text-gray-900">{title}</div>
      {!compact && description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
      {cta && <div className="mt-3">{cta}</div>}
    </div>
  )
}

function formatDate(iso: string) {
  try {
    const d = new Date(iso)
    return d.toLocaleDateString("da-DK", { year: "numeric", month: "short", day: "numeric" })
  } catch {
    return iso
  }
}