import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getFilesCollection, getUsersCollection, getSessionsCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'
import { 
  generateFileId, 
  getFileExpiryDate, 
  getCurrentPeriodStart
} from '@/app/lib/utils'
import { ApiResponse, FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
export const maxDuration = 300
export const preferredRegion = 'auto'

export async function POST(request: NextRequest) {
  try {
    console.log('Upload-complete: Starting request')
    
    const body = await request.json()
    const { 
      uploadId, 
      filename, 
      size, 
      mimeType, 
      goFileResult,
      uploadServer 
    } = body

    console.log('Upload-complete: Parsed request', {
      uploadId,
      filename,
      size,
      mimeType,
      hasGoFileResult: !!goFileResult
    })

    if (!uploadId || !filename || !size || !mimeType || !goFileResult) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' } as ApiResponse,
        { status: 400 }
      )
    }

    // Check authentication or guest
    const session = await auth()
    const userEmail = session?.user?.email || undefined
    const isAuthed = Boolean(userEmail)
    const guestCookie = request.cookies.get('guest_session_id')
    const sessionsCol = await getSessionsCollection()
    let guestSessionId = guestCookie?.value
    let user: any = null
    let effectivePlan: 'guest' | 'free' | 'upgrade1' | 'upgrade2' = 'guest'

    if (isAuthed) {
      const users = await getUsersCollection()
      user = await users.findOne({ email: userEmail })
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' } as ApiResponse,
          { status: 404 }
        )
      }
      effectivePlan = (user.plan || 'free')
    } else {
      if (!guestSessionId) {
        guestSessionId = `guest_${Math.random().toString(36).slice(2)}${Date.now().toString(36)}`
        await sessionsCol.insertOne({
          sessionId: guestSessionId,
          totalUploaded: 0,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          files: []
        })
      }
    }

    // Validate GoFile result
    if (!goFileResult || goFileResult.status !== 'ok') {
      return NextResponse.json(
        { success: false, error: 'GoFile upload failed', details: goFileResult } as ApiResponse,
        { status: 500 }
      )
    }

    // Build the download link
    const originalNameSanitized = filename.replace(/\//g, '_')
    let resolvedServer = goFileResult?.data?.server || uploadServer
    let resolvedFileIdUuid = goFileResult?.data?.id || goFileResult?.data?.fileId
    const shortCode = goFileResult?.data?.code || goFileResult?.data?.parentFolderCode

    // If we lack a UUID fileId, but have a short code, query GoFile getFile
    if (!resolvedFileIdUuid && shortCode) {
      try {
        const baseUrl = process.env.GOFILE_API_BASE_URL || 'https://api.gofile.io'
        const accountToken = process.env.GOFILE_ACCOUNT_TOKEN
        const infoUrl = `${baseUrl}/getFile/${shortCode}${accountToken ? `?token=${accountToken}` : ''}`
        const infoResp = await fetch(infoUrl, { method: 'GET' })
        const infoJson = await infoResp.json()
        
        if (infoResp.ok && infoJson?.status === 'ok' && infoJson?.data) {
          resolvedServer = infoJson.data.server || resolvedServer
          resolvedFileIdUuid = infoJson.data.fileId || resolvedFileIdUuid
        }
      } catch (e) {
        console.log('Failed to resolve GoFile file info via getFile:', e)
      }
    }

    // Construct direct link
    const safeServer = resolvedServer?.includes('gofile.io') ? resolvedServer : `${resolvedServer}.gofile.io`
    const constructedDirect = (safeServer && resolvedFileIdUuid)
      ? `https://${safeServer}/download/web/${resolvedFileIdUuid}/${encodeURIComponent(originalNameSanitized)}`
      : undefined

    const downloadUrl = goFileResult?.data?.directLink || constructedDirect

    if (!downloadUrl) {
      return NextResponse.json(
        { success: false, error: 'Failed to generate download URL' } as ApiResponse,
        { status: 500 }
      )
    }

    // Create file record
    const fileId = generateFileId()
    const expiryDate = getFileExpiryDate((effectivePlan as any))

    const fileRecord: Omit<FileRecord, '_id'> & { _id: ObjectId } = {
      _id: new ObjectId(),
      filename: fileId,
      originalName: filename,
      mimeType,
      size,
      uploadDate: new Date(),
      expiryDate,
      downloadCount: 0,
      downloadLimit: -1, // Unlimited for now
      isActive: true,
      storageProvider: 'GF',
      directLink: downloadUrl,
      ...(isAuthed ? { ownerId: user._id.toString() } : { sessionId: guestSessionId! }),
    }

    const files = await getFilesCollection()
    await files.insertOne(fileRecord)

    // Update usage
    if (isAuthed) {
      const users = await getUsersCollection()
      const usageField = effectivePlan === 'free' ? 'usage.monthly' : 'usage.weekly'
      await users.updateOne(
        { _id: user._id },
        { $inc: { [usageField]: size }, $set: { [`${usageField}UpdatedAt`]: new Date() } }
      )
    } else {
      await sessionsCol.updateOne(
        { sessionId: guestSessionId },
        { $inc: { totalUploaded: size }, $push: { files: fileRecord._id.toString() } }
      )
    }

    // Return public-safe payload only. Do not expose directLink or storage fields in API response.
    const response = NextResponse.json({
      success: true,
      data: {
        fileId,
        // keep downloadUrl for immediate post-upload UX if this endpoint is not directly used by end-users,
        // but if it is client-facing and must not expose a direct link, comment the next line out:
        // downloadUrl,
        message: 'File uploaded successfully'
      }
    } as ApiResponse)

    if (!isAuthed && guestCookie?.value !== guestSessionId) {
      response.cookies.set('guest_session_id', guestSessionId!, {
        httpOnly: true,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
        path: '/',
        maxAge: 7 * 24 * 60 * 60
      })
    }

    return response

  } catch (error) {
    console.error('Upload-complete error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Upload failed' } as ApiResponse,
      { status: 500 }
    )
  }
} 