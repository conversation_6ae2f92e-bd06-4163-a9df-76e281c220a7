import { NextRequest, NextResponse } from 'next/server';

// Enforce proxy-only small/medium uploads and never expose provider URLs
export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

// Align with standard cap: defer large files to /api/upload-large
const MAX_STANDARD_UPLOAD_BYTES =
  Number(process.env.MAX_STANDARD_UPLOAD_BYTES || '') ||
  500 * 1024 * 1024 // 500MB default

export async function POST(request: NextRequest) {
  try {
    const contentLength = parseInt(request.headers.get('content-length') || '0')
    if (contentLength && contentLength > MAX_STANDARD_UPLOAD_BYTES) {
      return NextResponse.json(
        {
          success: false,
          error: 'File too large for this endpoint',
          details: {
            maxBytes: MAX_STANDARD_UPLOAD_BYTES,
            maxMB: Math.floor(MAX_STANDARD_UPLOAD_BYTES / 1024 / 1024),
          },
          suggestion: 'Use /api/upload-large for big files'
        },
        { status: 413 }
      )
    }

    // Buffering formData is acceptable under standard cap
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Create FormData for GoFile.io (server-side only)
    const goFileFormData = new FormData();
    goFileFormData.append('file', file);

    const accountToken = process.env.GOFILE_ACCOUNT_TOKEN;
    const accountId = process.env.GOFILE_ACCOUNT_ID;

    if (accountToken) {
      goFileFormData.append('token', accountToken);
    }

    // Optional: resolve account root folder
    let folderId: string | undefined = undefined;
    if (accountToken && accountId) {
      try {
        const accountResponse = await fetch(`https://api.gofile.io/accounts/${accountId}?token=${accountToken}`);
        const responseText = await accountResponse.text();
        try {
          const accountData = JSON.parse(responseText);
          if (accountData.status === 'ok' && accountData.data?.rootFolder) {
            folderId = String(accountData.data.rootFolder);
            // Only append if defined (avoid TS null/undefined mismatch)
            if (folderId) {
              goFileFormData.append('folderId', folderId);
            }
          }
        } catch {
          // ignore parse errors; not critical
        }
      } catch {
        // ignore account fetch errors; proceed without folder
      }
    }

    // Server selection
    let uploadServer = 'store1.gofile.io';
    try {
      const serverResponse = await fetch('https://api.gofile.io/servers');
      const serverData = await serverResponse.json() as any;
      if (serverData.status === 'ok' && serverData.data?.servers?.length > 0) {
        uploadServer = serverData.data.servers[0].name + '.gofile.io';
      }
    } catch {
      // fallback to default
    }

    const uploadUrl = `https://${uploadServer}/contents/uploadfile`;

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: goFileFormData,
    });

    const resultText = await response.text();
    let result: any;
    try {
      result = JSON.parse(resultText);
    } catch {
      result = { raw: resultText };
    }

    if (!response.ok) {
      return NextResponse.json(
        { success: false, error: 'Provider upload failed', details: result },
        { status: response.status }
      );
    }

    // Never leak provider direct URLs in browser network. Only return minimal data.
    return NextResponse.json({
      success: true,
      data: {
        provider: 'gofile',
        result
      }
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
