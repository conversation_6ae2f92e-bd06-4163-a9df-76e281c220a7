import { <PERSON>, Card<PERSON>ontent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/app/components/ui/card"
import FeatureComparison from "@/app/components/FeatureComparison"

export const metadata = {
  title: "Hvorfor os | FlyFiles",
  description:
    "Hvorfor vælge FlyFiles – dansk fildeling, der er hurtig, sikker og enkel. Se vores fordele, ofte stillede spørgsmål og kom hurtigt i gang.",
}

export default function HvorforOsPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section (aligns with Kontakt/Bliv-partner style) */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-700 via-blue-800 to-blue-900 text-white py-16 sm:py-20 md:py-28">
        <div className="absolute inset-0 opacity-20 pointer-events-none">
          <div className="absolute -top-24 -left-16 h-72 w-72 rounded-full bg-blue-400 blur-3xl" />
          <div className="absolute top-1/3 -right-10 h-72 w-72 rounded-full bg-cyan-400 blur-3xl" />
          <div className="absolute bottom-0 left-1/4 h-56 w-56 rounded-full bg-indigo-500 blur-3xl" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6">
          <div className="max-w-3xl">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm text-blue-50 text-sm font-medium">
              Enkelt. Hurtigt. Dansk.
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold tracking-tight">
              Hvorfor vælge <span className="text-blue-200">FlyFiles</span>?
            </h1>
            <p className="mt-4 text-blue-100 text-lg md:text-xl">
              Vi gør fildeling simpelt, hurtigt og sikkert – bygget i Danmark, med gennemsigtige vilkår og fokus på privatliv.
            </p>
          </div>
        </div>
      </section>

      {/* Value props in Cards */}
      <section className="py-12 sm:py-16 bg-gradient-to-b from-white to-blue-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight text-gray-900">Det får du med FlyFiles</h2>
            <p className="mt-2 text-gray-600">Designet til hastighed, sikkerhed og en god oplevelse – fra dag ét.</p>
          </div>

          <div className="mt-8 grid gap-6 sm:gap-8 md:grid-cols-3">
            <Card className="border-blue-100 shadow-sm">
              <CardHeader>
                <CardTitle>Høj hastighed</CardTitle>
                <CardDescription>Optimeret upload/download og stabil infrastruktur.</CardDescription>
              </CardHeader>
              <CardContent className="text-gray-700">
                Hurtige overførsler – også for store filer – så arbejdet ikke går i stå.
              </CardContent>
            </Card>

            <Card className="border-blue-100 shadow-sm">
              <CardHeader>
                <CardTitle>Sikkerhed først</CardTitle>
                <CardDescription>Krypterede forbindelser, kontrol og automatisk sletning.</CardDescription>
              </CardHeader>
              <CardContent className="text-gray-700">
                Privatliv og tryghed som standard – gennemtænkt databehandling.
              </CardContent>
            </Card>

            <Card className="border-blue-100 shadow-sm">
              <CardHeader>
                <CardTitle>Dansk support</CardTitle>
                <CardDescription>Hjælp, når du har brug for det – på dansk.</CardDescription>
              </CardHeader>
              <CardContent className="text-gray-700">
                Tæt dialog og hurtige svar fra et team, der forstår dine behov.
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Feature Comparison (collapsible) */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <FeatureComparison
          className="py-12 sm:py-16"
          title="Hvorfor vælge FlyFiles frem for WeTransfer?"
          subtitle="Sammenligning af nøglefunktioner baseret på vores implementerede funktioner og offentlige information."
          otherLabel="WeTransfer (gratis)"
          initialVisibleRows={3}
          rows={[
            {
              title: "Store uploads med live fremdrift",
              description: "Chunked upload med præcis fremdrift, hastighed og ETA.",
              flyfiles: "yes",
              other: "Basal status; ingen per-fil live ETA i gratis version",
            },
            {
              title: "Konfigurerbare downloadgrænser",
              description: "Begræns downloads per fil på kontoplaner.",
              flyfiles: "yes",
              other: "Ikke tilgængelig i gratis; uklar i basisgratis",
            },
            {
              title: "Data i EU og stram GDPR-overholdelse",
              description: "Data opbevares i Europa og følger danske/europæiske regler.",
              flyfiles: "yes",
              other: "Global infrastruktur; EU-placering kan variere efter plan",
            },
            {
              title: "Ingen indholds-analyse eller AI-træning",
              description: "Vi analyserer ikke dine filer og bruger dem ikke til AI-træning.",
              flyfiles: "yes",
              other: "Ikke tydeligt i gratis-materiale",
            },
            {
              title: "Sikre links og automatisk sletning",
              description: "Udløb styres af din plan; filer slettes automatisk efter udløb.",
              flyfiles: "yes",
              other: "Ja, men detaljer varierer på tværs af planer",
            },
            {
              title: "Gæste-uploads uden konto",
              description: "Hurtig deling uden konto. Op til 250MB per session.",
              flyfiles: "yes",
              other: "Kræver e-mail i gratisflow",
            },
            {
              title: "Per-fil hastighed og ETA",
              description: "Live hastighed og estimeret tid ud fra bytes/sek pr. fil.",
              flyfiles: "yes",
              other: "Ikke fremhævet i gratisflow",
            },
          ]}
        />
      </div>

      {/* Why section as two Cards, mirroring Bliv-partner layout */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="grid lg:grid-cols-2 gap-6 sm:gap-8">
            <Card className="border-blue-100">
              <CardHeader>
                <CardTitle>Hvorfor FlyFiles?</CardTitle>
                <CardDescription>Fordi deling af store filer skal være nemt, hurtigt og trygt.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2 text-gray-700">
                <ul className="list-disc pl-5 space-y-2">
                  <li>Enkel brugeroplevelse – ingen kompleks opsætning</li>
                  <li>Hurtige og stabile overførsler til hverdagens arbejde</li>
                  <li>Gennemsigtige priser – ingen skjulte gebyrer</li>
                  <li>Bygget i Danmark – med respekt for privatliv</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-blue-100">
              <CardHeader>
                <CardTitle>Passer til dit behov</CardTitle>
                <CardDescription>Fleksible grænser og muligheder – solo, team eller virksomhed.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2 text-gray-700">
                <ul className="list-disc pl-5 space-y-2">
                  <li>Del nemt med kunder, kolleger og samarbejdspartnere</li>
                  <li>Tilpas opbevaring og delingsgrænser efter behov</li>
                  <li>Mulighed for ekstra support og funktioner, når du vokser</li>
                  <li>Integrationer til populære værktøjer – når du har brug for det</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ, same pattern as Bliv-partner */}
      <section className="py-4 sm:py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <h2 className="text-2xl md:text-3xl font-extrabold text-gray-900 mb-6">Ofte stillede spørgsmål</h2>
          <div className="grid md:grid-cols-2 gap-6 sm:gap-8">
            {[
              {
                q: "Hvordan er FlyFiles anderledes?",
                a: "Vi fokuserer på enkelhed, hastighed og dansk support. Du får gennemsigtige vilkår og en tryg løsning uden støj.",
              },
              {
                q: "Hvor sikre er mine filer?",
                a: "Overførsler sker via krypterede forbindelser, og du har kontrol over opbevaring og automatisk sletning.",
              },
              {
                q: "Hvad koster det?",
                a: <span>Se vores <a href="/pricing" className="text-blue-600 hover:underline">priser</a> og vælg en plan, der passer til dit behov. Ingen skjulte gebyrer.</span>,
              },
              {
                q: "Kan jeg få hjælp, hvis jeg sidder fast?",
                a: "Ja. Du kan skrive til os – vi svarer hurtigt og hjælper dig videre.",
              },
            ].map((f) => (
              <Card key={f.q} className="border-blue-100">
                <CardHeader>
                  <CardTitle>{f.q}</CardTitle>
                  <CardDescription>{f.a}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA, consistent with other pages */}
      <section className="py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <Card className="border-blue-100 shadow-sm">
            <CardContent className="p-6 md:p-10">
              <div className="grid md:grid-cols-2 gap-6 items-center">
                <div>
                  <h3 className="text-2xl md:text-3xl font-extrabold text-gray-900">
                    Klar til at prøve FlyFiles?
                  </h3>
                  <p className="mt-2 text-gray-700">
                    Start enkelt og uden bøvl – del store filer hurtigt og sikkert. Dansk support, når du har brug for det.
                  </p>
                </div>
                <div className="flex flex-wrap gap-3 md:justify-end justify-center">
                  <a href="/pricing" className="inline-block rounded-md">
                    <button className="inline-flex items-center justify-center h-10 px-6 rounded-md bg-blue-700 text-white hover:bg-blue-800 transition-colors">
                      Se priser
                    </button>
                  </a>
                  <a href="/kontakt" className="inline-block rounded-md">
                    <button className="inline-flex items-center justify-center h-10 px-6 rounded-md border border-blue-600 text-blue-700 hover:bg-blue-50 transition-colors">
                      Kontakt os
                    </button>
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </main>
  )
}