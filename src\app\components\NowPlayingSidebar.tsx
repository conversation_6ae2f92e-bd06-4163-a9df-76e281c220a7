"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Music2 } from "lucide-react";
import { FaSpotify } from "react-icons/fa";

type ApiTrack = {
  source?: 'spotify' | 'lastfm';
  isPlaying: boolean;
  title: string | null;
  artists: string | null;
  album: string | null;
  image: string | null;
  url: string | null;
  durationMs?: number | null;
  progressMs?: number | null;
  playedAt?: string | null;
};

function msToTime(ms?: number | null) {
  if (!ms && ms !== 0) return '0:00';
  const totalSeconds = Math.max(0, Math.floor(ms / 1000));
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

export default function NowPlayingSidebar() {
  const [track, setTrack] = useState<ApiTrack | null>(null);
  const [progressMs, setProgressMs] = useState<number>(0);
  const lastStartRef = useRef<number | null>(null);

  const effectiveDuration = track?.durationMs ?? null;

  const hasTiming = useMemo(() => {
    if (!track || !track.isPlaying) return false;
    const hasDur = typeof track.durationMs === 'number' && Number.isFinite(track.durationMs);
    const hasProg = typeof track.progressMs === 'number' && Number.isFinite(track.progressMs);
    return hasDur && hasProg;
  }, [track]);

  // Fetch helper
  const fetchData = useCallback(async () => {
    try {
      const res = await fetch('/api/lastfm', { cache: 'no-store' });
      const json = await res.json();
      setTrack(json);
      const baseProgress = typeof json.progressMs === 'number' ? json.progressMs : 0;
      setProgressMs(baseProgress);
      lastStartRef.current = Date.now();
    } catch {
      // ignore
    }
  }, []);

  // Initial fetch and visibility refresh
  useEffect(() => {
    let active = true;
    fetchData();
    const onVis = () => {
      if (document.visibilityState === 'visible') fetchData();
    };
    document.addEventListener('visibilitychange', onVis);
    return () => {
      active = false;
      document.removeEventListener('visibilitychange', onVis);
    };
  }, [fetchData]);

  // Smart refresh scheduling: frequent checks when not playing or when skipping; precise refresh at track end
  useEffect(() => {
    let intervalId: number | null = null;
    let timeoutId: number | null = null;

    const clearAll = () => {
      if (intervalId) clearInterval(intervalId);
      if (timeoutId) clearTimeout(timeoutId);
    };

    // If playing and we know duration/progress, schedule an exact refresh at expected end
    if (hasTiming) {
      const duration = track?.durationMs ?? 0;
      const progress = track?.progressMs ?? 0;
      const remaining = Math.max(0, duration - progress);
      // Backstop: check every 2000ms to catch manual skips earlier
      intervalId = window.setInterval(fetchData, 2000);
      timeoutId = window.setTimeout(() => {
        fetchData();
      }, Math.min(remaining + 250, 5 * 60_000));
    } else {
      // Not playing or unknown duration: poll faster to catch new playback quickly
      intervalId = window.setInterval(fetchData, 2000);
    }

    return () => {
      clearAll();
    };
  }, [hasTiming, track?.durationMs, track?.progressMs, fetchData]);

  // Animate progress only when we have reliable timing (e.g. Spotify)
  useEffect(() => {
    if (!hasTiming) return;

    const raf = () => {
      if (!track) return;
      const start = lastStartRef.current;
      if (start == null) return;
      const elapsed = Date.now() - start;
      const next = Math.min((track.progressMs || 0) + elapsed, track.durationMs || 0);
      setProgressMs(next);
      anim = requestAnimationFrame(raf);
    };

    let anim = requestAnimationFrame(raf);
    return () => cancelAnimationFrame(anim);
  }, [hasTiming, track?.durationMs, track?.progressMs]);

  const progressPct = useMemo(() => {
    if (!hasTiming) return 0;
    return Math.max(0, Math.min(100, (progressMs / (track?.durationMs || 1)) * 100));
  }, [hasTiming, track?.durationMs, progressMs]);

  return (
    <aside className="hidden lg:block lg:sticky lg:top-24 self-start w-full max-w-xs">
      <div className="relative rounded-2xl border border-blue-100 bg-white p-4 shadow-sm">
        {!track ? (
          <div className="animate-pulse">
            <div className="flex items-center gap-4">
              <div className="h-20 w-20 rounded-xl bg-gray-200" />
              <div className="min-w-0 flex-1 space-y-2">
                <div className="h-4 w-3/4 rounded bg-gray-200" />
                <div className="h-3 w-1/2 rounded bg-gray-200" />
                <div className="h-3 w-2/3 rounded bg-gray-200" />
              </div>
            </div>
            <div className="mt-4 h-2 w-full rounded bg-gray-100" />
          </div>
        ) : (
          <>
            <div className="flex items-start gap-4">
              <div className="relative h-20 w-20 overflow-hidden rounded-xl bg-blue-50 text-blue-600 flex items-center justify-center">
                {track.image ? (
                  // eslint-disable-next-line @next/next/no-img-element
                  <img src={track.image} alt="Album cover" className="h-full w-full object-cover" />
                ) : (
                  <Music2 className="h-8 w-8" />
                )}
                <span
                  className={`absolute top-2 left-2 inline-flex items-center rounded-full px-2 py-0.5 text-[11px] font-medium ${track.isPlaying ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`}
                >
                  {track.isPlaying ? 'Nu spiller' : 'Sidst lyttet'}
                </span>
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-start gap-2">
                  <div className="min-w-0">
                    <div className="text-base font-semibold text-gray-900 break-words">
                      {track.title ?? 'Ingen sang'}
                    </div>
                    <div className="truncate text-sm text-gray-600">{track.artists ?? '—'}</div>
                    {track.album && (
                      <div className="truncate text-xs text-gray-500">{track.album}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {hasTiming ? (
              <div className="mt-4">
                <div className="h-2 w-full overflow-hidden rounded bg-gray-100">
                  <div
                    className="h-full rounded bg-gradient-to-r from-blue-600 to-cyan-500 transition-[width] duration-300"
                    style={{ width: `${progressPct}%` }}
                  />
                </div>
                <div className="mt-1 flex justify-between text-xs text-gray-500">
                  <span>{msToTime(progressMs)}</span>
                  <span>{msToTime(track?.durationMs || 0)}</span>
                </div>
              </div>
            ) : (
              <div className="mt-3 text-xs text-gray-500">
                {track.isPlaying ? 'Spiller nu' : track.playedAt ? new Date(track.playedAt).toLocaleString() : '—'}
              </div>
            )}

          </>
        )}
        {(track?.url?.includes('spotify.com') ?? false) ? (
          <a
            href={track?.url || '#'}
            target="_blank"
            rel="noopener noreferrer"
            aria-label="Open in Spotify"
            title="Open in Spotify"
            className="absolute bottom-2 right-2 inline-flex items-center justify-center rounded-full bg-[#1DB954] p-2 text-white shadow transition hover:shadow-md"
          >
            <FaSpotify className="h-4 w-4" />
          </a>
        ) : (
          <div
            aria-label="Spotify"
            title="Spotify"
            className="absolute bottom-2 right-2 inline-flex items-center justify-center rounded-full bg-[#1DB954] p-2 text-white shadow"
          >
            <FaSpotify className="h-4 w-4" />
          </div>
        )}
      </div>
    </aside>
  );
}