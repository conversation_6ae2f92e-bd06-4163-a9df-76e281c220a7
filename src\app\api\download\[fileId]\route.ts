import { NextResponse, type NextRequest } from 'next/server'
import { getFilesCollection, getDownloadLogsCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'

export const runtime = 'nodejs'

/**
 * Streams file from Gofile through our server, completely hiding <PERSON><PERSON><PERSON> from the client.
 * Supports partial downloads (Range requests) and proper content headers.
 * Includes Gofile account token authentication to access actual files.
 */

function pickHeaders(headers: Headers, keys: string[]) {
  const out = new Headers()
  for (const key of keys) {
    const value = headers.get(key)
    if (value) out.set(key, value)
  }
  return out
}

function looksLikeHtml(headers: Headers) {
  const contentType = (headers.get('content-type') || '').toLowerCase()
  // Only consider it HTML if it's specifically text/html, not other text types
  return contentType.includes('text/html')
}

async function fetchUpstream(url: string, range: string | null, withCookieToken?: string) {
  const headers: Record<string, string> = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Upgrade-Insecure-Requests': '1'
  }
  
  // Add referer if it's a gofile domain
  if (url.includes('gofile.io')) {
    headers['Referer'] = 'https://gofile.io/'
  }
  
  if (range) {
    headers['Range'] = range
  }
  
  if (withCookieToken) {
    headers['Cookie'] = `accountToken=${withCookieToken}`
  }
  
  return fetch(url, { 
    headers, 
    redirect: 'follow' 
  })
}

async function getValidGofileToken(): Promise<string | null> {
  // First try environment token
  const envToken = process.env.GOFILE_ACCOUNT_TOKEN
  if (envToken) {
    return envToken
  }

  // Fallback: create guest account
  try {
    const response = await fetch('https://api.gofile.io/createAccount', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    const data = await response.json()
    
    if (data?.status === 'ok' && data?.data?.token) {
      return data.data.token
    }
  } catch (error) {
    console.error('Failed to create guest account:', error)
  }

  return null
}

async function getFreshDownloadLink(file: any, token: string): Promise<string | null> {
  try {
    // Extract file ID from directLink or use storageKey
    const fileId = file.storageKey || extractFileIdFromDirectLink(file.directLink)
    
    if (!fileId) {
      return file.directLink
    }
    
    // Use Gofile API to get fresh content info
    const apiResponse = await fetch(`https://api.gofile.io/getContent?contentId=${fileId}&token=${token}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    // Check if response is ok before trying to parse JSON
    if (!apiResponse.ok) {
      return file.directLink
    }
    
    const responseText = await apiResponse.text()
    
    // Check if it's an error string instead of JSON
    if (responseText.startsWith('error-') || !responseText.startsWith('{')) {
      return file.directLink
    }
    
    const apiData = JSON.parse(responseText)
    
    if (apiData?.status === 'ok' && apiData?.data?.contents) {
      // Find the file in the contents
      const contents = apiData.data.contents
      const fileEntry = Object.values(contents).find((item: any) => 
        item.name === file.originalName || item.name.includes(file.originalName)
      ) as any
      
      if (fileEntry && fileEntry.link) {
        return fileEntry.link
      }
    }
    
    return file.directLink
    
  } catch (error) {
    return file.directLink
  }
}

function extractFileIdFromDirectLink(directLink: string): string | null {
  try {
    // Extract from URLs like: https://store-eu-par-5.gofile.io/download/web/dbdb68ca-1d62-4d29-ab82-5872299f71b8/filename
    const match = directLink.match(/\/download\/web\/([a-f0-9-]+)\//)
    return match ? match[1] : null
  } catch {
    return null
  }
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await context.params
    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID required' } as ApiResponse,
        { status: 400 }
      )
    }

    const files = await getFilesCollection()
    // Accept either public filename (preferred) or Mongo _id for backward compatibility
    let file = await files.findOne({ filename: fileId, isActive: true })
    if (!file && typeof fileId === 'string') {
      try {
        const { ObjectId } = await import('mongodb')
        if (ObjectId.isValid(fileId)) {
          const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true })
          if (byId) file = byId
        }
      } catch {
        // ignore
      }
    }

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File not found or expired' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check expiry
    if (new Date() > new Date(file.expiryDate)) {
      await files.updateOne({ _id: file._id }, { $set: { isActive: false } })
      return NextResponse.json(
        { success: false, error: 'File has expired' } as ApiResponse,
        { status: 410 }
      )
    }

    // Check download limits
    if (file.downloadLimit !== -1 && file.downloadCount >= file.downloadLimit) {
      return NextResponse.json(
        { success: false, error: 'Download limit reached' } as ApiResponse,
        { status: 429 }
      )
    }

    const directLink: string | undefined = (file as any).directLink
    if (!directLink) {
      return NextResponse.json(
        { success: false, error: 'Direct link not available for this file' } as ApiResponse,
        { status: 500 }
      )
    }

    // Get filename from query parameter or fall back to original name
    const url = new URL(request.url)
    const queryFilename = url.searchParams.get('filename')
    const downloadFilename = queryFilename || file.originalName || 'download'

    // Log the download intent
    const downloadLogs = await getDownloadLogsCollection()
    const userAgent = request.headers.get('user-agent') || 'Unknown'
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown'

    await downloadLogs.insertOne({
      fileId: file._id.toString(),
      downloadedAt: new Date(),
      ip,
      userAgent
    })

    // Increment download count
    await files.updateOne({ _id: file._id }, { $inc: { downloadCount: 1 } })

    // Get Range header for partial downloads
    const rangeHeader = request.headers.get('range')

    // Get a valid token
    const token = await getValidGofileToken()
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication service unavailable' } as ApiResponse,
        { status: 503 }
      )
    }

    // For now, just use the direct link since it's working with authentication
    // The fresh API link fetching can be added later when we understand the API better
    const downloadUrl = directLink

    // Fetch the file with token
    const upstreamResponse = await fetchUpstream(downloadUrl, rangeHeader, token)

    if (!upstreamResponse.ok && upstreamResponse.status !== 206) {
      return NextResponse.json(
        { success: false, error: 'File temporarily unavailable' } as ApiResponse,
        { status: 502 }
      )
    }

    // Check if it's still HTML
    if (looksLikeHtml(upstreamResponse.headers)) {
      return NextResponse.json(
        { success: false, error: 'File access blocked - invalid link or permissions' } as ApiResponse,
        { status: 502 }
      )
    }

    // Prepare response headers - copy safe headers from upstream
    const responseHeaders = pickHeaders(upstreamResponse.headers, [
      'content-type',
      'content-length', 
      'accept-ranges',
      'content-range',
      'last-modified',
      'etag',
      'cache-control'
    ])

    // Set Content-Disposition for download with proper filename
    const fallbackFilename = downloadFilename || 
      decodeURIComponent(downloadUrl.split('/').pop() || 'download')
    
    responseHeaders.set('Content-Disposition', `attachment; filename="${fallbackFilename}"`)
    
    // Set additional cache headers if not already set
    if (!responseHeaders.has('cache-control')) {
      responseHeaders.set('Cache-Control', 'public, max-age=31536000, immutable')
    }

    // Stream the response body directly from upstream
    return new NextResponse(upstreamResponse.body, {
      status: upstreamResponse.status, // 200 or 206 for partial content
      headers: responseHeaders
    })

  } catch (error) {
    console.error('Error processing download:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

// HEAD request for checking file availability without logging download
export async function HEAD(
  request: NextRequest,
  context: { params: Promise<{ fileId: string }> }
) {
  try {
    const { fileId } = await context.params
    const files = await getFilesCollection()
    let file = await files.findOne({ filename: fileId, isActive: true })
    if (!file && typeof fileId === 'string') {
      try {
        const { ObjectId } = await import('mongodb')
        if (ObjectId.isValid(fileId)) {
          const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true })
          if (byId) file = byId
        }
      } catch {
        // ignore
      }
    }
    
    if (!file || new Date() > new Date(file.expiryDate)) {
      return new NextResponse(null, { status: 404 })
    }

    return new NextResponse(null, {
      status: 200,
      headers: {
        'Content-Length': file.size?.toString() || '0',
        'Content-Type': file.mimeType || 'application/octet-stream',
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    })
  } catch (error) {
    return new NextResponse(null, { status: 500 })
  }
}