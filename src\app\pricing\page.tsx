"use client"

import { useState } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Check, <PERSON>, Star, X } from "lucide-react"
import { PLAN_CONFIGS } from "@/app/lib/plans"
import { formatFileSize } from "@/app/lib/utils"
import SecureFiles from "@/app/components/securefiles"
import FeatureComparison from "@/app/components/FeatureComparison"

const plans = [
  {
    id: 'guest',
    config: PLAN_CONFIGS.guest,
    popular: false,
    action: 'Start som gæst',
    href: '/'
  },
  {
    id: 'free',
    config: PLAN_CONFIGS.free,
    popular: true,
    action: 'Log ind gratis',
    href: '/login'
  },
  {
    id: 'upgrade1',
    config: PLAN_CONFIGS.upgrade1,
    popular: false,
    action: 'Vælg Basis',
    href: '/login'
  },
  {
    id: 'upgrade2',
    config: PLAN_CONFIGS.upgrade2,
    popular: false,
    action: 'Vælg Pro',
    href: '/login'
  }
]

export default function PricingPage() {
  const [secureFilesOpen, setSecureFilesOpen] = useState(false)

  return (
    <>
      <SecureFiles
        isOpen={secureFilesOpen}
        onClose={() => setSecureFilesOpen(false)}
      />
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 md:py-28 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[url('/home-pattern.svg')] bg-repeat bg-center"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
              Transparent prissætning
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 sm:mb-6 leading-tight">
              Vælg din <span className="text-blue-300">plan</span>
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Fra gratis gæsteadgang til professionelle løsninger. Alle planer inkluderer sikker kryptering og automatisk fil-sletning.
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">


        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {plans.map((plan) => (
            <div key={plan.id} className="group flex flex-col">
              <Card className={`relative flex-1 flex flex-col transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] hover:shadow-2xl hover:shadow-blue-200/60 hover:-translate-y-2 ${
                plan.popular
                  ? 'border-blue-500 shadow-lg shadow-blue-100/50 scale-105 bg-gradient-to-b from-blue-50 to-white hover:from-blue-100 hover:to-blue-50'
                  : 'border-blue-100 hover:border-blue-400 bg-white hover:bg-gradient-to-b hover:from-blue-50/30 hover:to-white'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 transition-all duration-500 group-hover:scale-110">
                    <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-full text-sm font-bold flex items-center space-x-1 shadow-lg transition-all duration-500 group-hover:from-blue-700 group-hover:to-blue-800 group-hover:shadow-xl">
                      <Star className="h-3 w-3 transition-transform duration-500 group-hover:rotate-12" />
                      <span>Populær</span>
                    </div>
                  </div>
                )}

                <CardHeader className="text-center transition-all duration-500 group-hover:scale-105">
                  <CardTitle className="text-xl text-gray-800 transition-colors duration-500 group-hover:text-blue-700">{plan.config.name}</CardTitle>
                  <CardDescription className="text-center transition-all duration-500 group-hover:scale-105">
                    {'price' in plan.config ? (
                      <div>
                        <span className="text-3xl font-bold text-gray-800 transition-colors duration-500 group-hover:text-blue-600">
                          {plan.config.price.amount} kr
                        </span>
                        <span className="text-gray-600 transition-colors duration-500 group-hover:text-blue-500">/{plan.config.price.period}</span>
                      </div>
                    ) : (
                      <span className="text-3xl font-bold text-gray-800 transition-colors duration-500 group-hover:text-blue-600">Gratis</span>
                    )}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4 flex-1 flex flex-col">
                  <div className="space-y-2 flex-1">
                    <div className="text-center mb-4 transition-all duration-500 group-hover:scale-105">
                      <div className="text-lg font-bold text-blue-600 transition-colors duration-500 group-hover:text-blue-700">
                        {formatFileSize(plan.config.uploadLimit.amount)}
                      </div>
                      <div className="text-sm text-gray-600 transition-colors duration-500 group-hover:text-blue-500">
                        per {plan.config.uploadLimit.period === 'session' ? 'session' :
                             plan.config.uploadLimit.period === 'uge' ? 'uge' : 'måned'}
                      </div>
                    </div>

                    <ul className="space-y-3">
                      {plan.config.features.map((feature, index) => (
                        <li key={index} className="flex items-start space-x-3 transition-all duration-500 group-hover:translate-x-1" style={{ transitionDelay: `${index * 50}ms` }}>
                          <div className={`w-5 h-5 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0 transition-all duration-500 group-hover:scale-110 ${
                            feature.includes("Reklamer") && !plan.config.name.includes("Gæst")
                              ? "bg-red-100 group-hover:bg-red-200" 
                              : "bg-green-100 group-hover:bg-green-200"
                          }`}>
                            {feature.includes("Reklamer") && !plan.config.name.includes("Gæst") ? (
                              <X className="h-3 w-3 text-red-600 transition-all duration-500 group-hover:text-red-700" />
                            ) : (
                              <Check className="h-3 w-3 text-green-600 transition-all duration-500 group-hover:text-green-700" />
                            )}
                          </div>
                          <span className="text-sm text-gray-700 transition-colors duration-500 group-hover:text-gray-800">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <a href={plan.href} className="block mt-auto">
                    <Button
                      className="w-full transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:scale-110 group-hover:shadow-xl"
                      variant={plan.popular ? "default" : "outline"}
                    >
                      <span className="flex items-center justify-center">
                        {plan.action}
                        <svg className="w-4 h-4 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-2 group-hover:scale-110" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </span>
                    </Button>
                  </a>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <section className="bg-white py-16">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 mb-4">
                Ofte stillede <span className="text-blue-600">spørgsmål</span>
              </h2>
              <p className="text-xl text-gray-600">
                Find svar på de mest almindelige spørgsmål
              </p>
            </div>

            <div className="space-y-6">
              <div className="group">
                <Card className="border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-800">Hvordan fungerer gæsteadgang?</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Som gæst kan du uploade op til 250MB per browsersession uden at oprette en konto.
                      Dine filer bliver automatisk slettet efter 7 dage.
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="group">
                <Card className="border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-800">Hvad sker der med mine filer efter udløb?</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Alle filer bliver automatisk og permanent slettet fra vores servere når de udløber.
                      Dette sikrer din privatliv og overholder GDPR-reglerne.
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="group">
                <Card className="border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-800">Kan jeg opgradere eller nedgradere min plan?</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Ja, du kan til enhver tid ændre din plan. Ved opgraderinger får du adgang til de nye funktioner med det samme.
                      Ved nedgraderinger træder ændringerne i kraft ved næste faktureringsperiode.
                    </p>
                  </CardContent>
                </Card>
              </div>
              <div className="group">
                <Card className="border-blue-100 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 bg-white">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-800">Er mine filer sikre?</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Ja, alle filer krypteres under upload og opbevaring. Vi bruger industri-standard sikkerhed
                      og overholder danske GDPR-regler for databeskyttelse.
                    </p>
                    <button 
                      onClick={() => setSecureFilesOpen(true)}
                      className="mt-4 text-blue-600 hover:text-blue-800 font-medium flex items-center"
                    >
                      Læs mere
                      <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>



        {/* Call to Action */}
        <section className="py-20 bg-gradient-to-r from-blue-700 to-blue-900 text-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">
              Klar til at komme i gang?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Start med vores gratis plan eller prøv som gæst
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/login">
                <button className="group relative bg-white text-blue-800 px-8 py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20">
                  <span className="relative z-10 flex items-center justify-center">
                    Opret gratis konto
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </button>
              </a>
              <a href="/">
                <button className="group relative bg-transparent border-2 border-white text-white px-8 py-4 rounded-xl font-bold overflow-hidden transition-all duration-500 hover:bg-white hover:text-blue-800 hover:shadow-2xl hover:shadow-blue-500/20">
                  <span className="relative z-10 flex items-center justify-center">
                    Prøv som gæst
                    <svg className="w-5 h-5 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                </button>
              </a>
            </div>
          </div>
        </section>
      </div>
    </main>
    </>
  )
}