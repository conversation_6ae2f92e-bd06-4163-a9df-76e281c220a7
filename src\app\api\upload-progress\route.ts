import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getUploadProgress } from '@/app/lib/upload-progress'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const session = await auth()
  const hasGuest = Boolean(request.cookies.get('guest_session_id'))
  if (!session?.user?.email && !hasGuest) {
    return new Response('Unauthorized', { status: 401 })
  }

  const uploadId = request.nextUrl.searchParams.get('uploadId')

  if (!uploadId) {
    return new Response('Missing uploadId', { status: 400 })
  }

  const stream = new ReadableStream({
    start(controller) {
      let lastProgress = -1
      let lastStatus = ''
      let errorCount = 0
      const maxErrors = 10
      
      const sendProgress = async () => {
        try {
          const progress = await getUploadProgress(uploadId)
          
          if (progress) {
            // Only send update if progress or status changed
            if (progress.progress !== lastProgress || progress.status !== lastStatus) {
              const data = JSON.stringify({
                progress: progress.progress,
                status: progress.status,
                error: progress.error
              })
              
              controller.enqueue(new TextEncoder().encode(`data: ${data}\n\n`))
              
              lastProgress = progress.progress
              lastStatus = progress.status
              errorCount = 0 // Reset error count on successful update
              
              if (progress.status === 'completed' || progress.status === 'error') {
                controller.close()
                return
              }
            }
          } else {
            errorCount++
          }
          
          // Stop if too many consecutive errors
          if (errorCount >= maxErrors) {
            controller.close()
            return
          }
          
          // Continue polling
          setTimeout(sendProgress, 100)
        } catch (error) {
          console.error(`SSE: Error getting progress for ${uploadId}:`, error)
          errorCount++
          
          if (errorCount >= maxErrors) {
            controller.close()
            return
          }
          
          // Continue polling even on error
          setTimeout(sendProgress, 1000) // Slower polling on error
        }
      }
      
      sendProgress()
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
} 