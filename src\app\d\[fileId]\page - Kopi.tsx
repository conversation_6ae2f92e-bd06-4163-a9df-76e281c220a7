import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getFilesCollection } from '@/app/lib/mongodb'
import { type FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

/**
 * Interactive copy button is a Client Component imported normally.
 * In a Server Component, avoid next/dynamic with ssr: false.
 * Client boundaries render fine when imported directly.
 */
import { CopyLinkButton } from '@/app/d/[fileId]/copy-link-client'
import DownloadButton from './DownloadButton'

export default async function DownloadPage({
  params,
}: {
  params: Promise<{ fileId: string }>
}) {
  const { fileId } = await params
  const files = await getFilesCollection()

  // Prefer public filename for shared links
  let file = (await files.findOne({ filename: fileId, isActive: true })) as unknown as FileRecord | null

  // Fallback: allow direct _id usage (older copied links)
  if (!file && ObjectId.isValid(fileId)) {
    const byId = (await files.findOne({ _id: new ObjectId(fileId), isActive: true })) as unknown as FileRecord | null
    if (byId) file = byId
  }

  if (!file) {
    notFound()
  }

  // Check if file is expired or limit reached
  const limitReached = file.downloadLimit !== -1 && file.downloadCount >= file.downloadLimit

  const sizeFormatter = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
  }

  const expired = new Date() > new Date(file!.expiryDate)
  const remaining = file!.downloadLimit !== -1 ? Math.max(0, file!.downloadLimit - (file!.downloadCount ?? 0)) : undefined

  // Build absolute page URL for copy UX. Fallback to relative if env missing.
  const base = process.env.NEXT_PUBLIC_BASE_URL || ''
  const pageUrl = `${base}/d/${file!.filename}`

  return (
    <main className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <section className="relative py-12 sm:py-16">
        <div className="mx-auto max-w-3xl px-4 sm:px-6">
          <div className="overflow-hidden rounded-2xl border border-blue-100 bg-white shadow-xl shadow-blue-100/50">
            <div className="relative bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-8 sm:px-8">
              <div className="flex items-start gap-4">
                <div className="rounded-xl bg-white/10 p-3 text-white">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <path d="M12 3v14m0 0l-5-5m5 5l5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">{file!.originalName}</h1>
                  <p className="text-sm text-blue-100">{file!.mimeType} • {sizeFormatter(file!.size)}</p>
                </div>
              </div>

              <div className="mt-4 flex flex-wrap items-center gap-3 text-sm">
                <span className="rounded-full bg-white/10 px-3 py-1 text-blue-100">
                  Udløber: {new Date(file!.expiryDate).toLocaleString('da-DK')}
                </span>
                {typeof remaining !== 'undefined' && (
                  <span className="rounded-full bg-white/10 px-3 py-1 text-blue-100">
                    Downloads tilbage: {remaining}
                  </span>
                )}
                {expired && (
                  <span className="rounded-full bg-red-600/90 px-3 py-1 text-white">
                    Udløbet
                  </span>
                )}
              </div>
            </div>

            <div className="px-6 py-8 sm:px-8">
              <div className="grid gap-6 sm:grid-cols-[1fr_auto] sm:items-center">
                <div className="space-y-2">
                  <p className="text-gray-600">
                    Denne side giver sikker adgang til din fil. Hvis knappen er inaktiv, er filen udløbet eller ikke længere tilgængelig.
                  </p>
                  {typeof remaining !== 'undefined' && (
                    <div className="text-xs text-gray-500">
                      Du kan downloade denne fil op til {file!.downloadLimit} gange. Brugte: {file!.downloadCount ?? 0}
                    </div>
                  )}
                </div>
 
                <div className="flex flex-col gap-3 sm:justify-end">
                  <DownloadButton 
                    expired={expired || limitReached} 
                    fileId={file.filename}
                    filename={file.originalName}
                  />
 
                  <CopyLinkButton url={pageUrl} />
                  <div id="copy-status" className="text-center text-xs text-green-600 opacity-0 transition-opacity duration-300">
                    Kopieret!
                  </div>
                </div>
              </div>

              <div className="mt-8 rounded-xl border border-gray-100 bg-gray-50 px-4 py-3 text-xs text-gray-600">
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <div className="flex items-center gap-2">
                    <span className="inline-flex h-2 w-2 rounded-full bg-green-500"></span>
                    Sikker filoverførsel
                  </div>
                  <Link href="/" className="text-blue-700 hover:underline">
                    Til forsiden
                  </Link>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>
    </main>
  )
}