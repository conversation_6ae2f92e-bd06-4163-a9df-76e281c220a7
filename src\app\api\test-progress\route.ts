import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { updateUploadProgress, getUploadProgress } from '@/app/lib/upload-progress'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 })
    }

    const { uploadId, action } = await request.json()

    if (!uploadId) {
      return NextResponse.json({ success: false, error: 'Missing uploadId' }, { status: 400 })
    }

    if (action === 'init') {
      // Initialize progress
      await updateUploadProgress(uploadId, 0, 'starting')
      return NextResponse.json({ success: true, message: 'Progress initialized' })
    } else if (action === 'update') {
      // Update progress
      const { progress, status, error } = await request.json()
      await updateUploadProgress(uploadId, progress, status, error)
      return NextResponse.json({ success: true, message: 'Progress updated' })
    } else if (action === 'get') {
      // Get current progress
      const progress = await getUploadProgress(uploadId)
      return NextResponse.json({ success: true, progress })
    } else {
      return NextResponse.json({ success: false, error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Test progress error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Test failed' },
      { status: 500 }
    )
  }
} 