import Link from "next/link";
import NowPlayingSidebar from "@/app/components/NowPlayingSidebar";

export const metadata = {
  title: "Om FlyFiles | FlyFiles",
  description:
    "Lær mere om FlyFiles og ejeren 'Mickas' aka 'MyckasP' – passioneret udvikler på en mission om at gøre fildeling nem og billig.",
};

export default function OmFlyFilesPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-700 via-blue-800 to-blue-900 text-white py-16 sm:py-20 md:py-28">
        <div className="absolute inset-0 opacity-20 pointer-events-none">
          <div className="absolute -top-24 -left-16 h-72 w-72 rounded-full bg-blue-400 blur-3xl" />
          <div className="absolute top-1/3 -right-10 h-72 w-72 rounded-full bg-cyan-400 blur-3xl" />
          <div className="absolute bottom-0 left-1/4 h-56 w-56 rounded-full bg-indigo-500 blur-3xl" />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6">
          <div className="max-w-3xl">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm text-blue-50 text-sm font-medium">
              Om os
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold tracking-tight">
              Om <span className="text-blue-200">FlyFiles</span>
            </h1>
            <p className="mt-4 text-blue-100 text-lg md:text-xl">
              En dansk fildelingsplatform på en mission: at gøre fildeling enkel, hurtig og billig for alle.
            </p>
          </div>
        </div>
      </section>

      {/* Content with sticky left sidebar */}
      <section className="py-12 sm:py-16 bg-gradient-to-b from-white to-blue-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="grid lg:grid-cols-12 gap-8">
            <div className="lg:col-span-3">
              <NowPlayingSidebar />
            </div>
            <div className="lg:col-span-9">
              <div className="mx-auto max-w-3xl">
                <h2 className="text-2xl md:text-3xl font-extrabold tracking-tight text-gray-900 mb-4">
                  Ejeren bag FlyFiles
                </h2>
                <div className="bg-white rounded-2xl border border-blue-100 shadow-sm p-6 sm:p-8">
                  <p className="text-gray-700 leading-relaxed">
                    FlyFiles er skabt og ejet af <span className="font-semibold">"Mickas"</span> aka{" "}
                    <span className="font-semibold">MyckasP</span>. Han har en stor passion for
                    programmering og har bygget hjemmesider for både andre og sig selv. Nu har han startet en ny
                    rejse med FlyFiles – et projekt, der skal gøre fildeling let, tilgængeligt og billigt.
                  </p>
                  <p className="text-gray-700 leading-relaxed mt-4">
                    Visionen er klar: at hjælpe så mange som muligt med at dele filer uden besvær, uden fordyrende
                    løsninger og med respekt for brugernes tid og behov.
                  </p>

                  {/* MyckasP spotlight */}
                  <div className="mt-8 p-5 sm:p-6 rounded-xl border border-blue-100 bg-blue-50/40">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900">Hvem er “MyckasP”?</h3>
                    <p className="mt-2 text-gray-700 leading-relaxed">
                      “MyckasP” er online-aliaset for Mickas — en praktisk anlagt, dansk udvikler med fokus på
                      performance, lave omkostninger og en god brugeroplevelse. Han eksperimenterer ofte med nye
                      teknologier, automatisering og optimering af processer, og FlyFiles er kulminationen af mange
                      små projekter og erfaringer samlet i én enkel tjeneste.
                    </p>
                    <ul className="mt-4 space-y-2 text-gray-700 list-disc pl-5">
                      <li>Brænder for simple løsninger, der virker i praksis.</li>
                      <li>Går op i gennemsigtighed og fair priser for slutbrugeren.</li>
                      <li>Elsker at bygge, måle, forbedre — og gentage.</li>
                    </ul>

                    <div className="mt-5 grid gap-3 sm:grid-cols-2">
                      <Link
                        href="/om-os/hvorfor-flyfiles-findes"
                        className="inline-flex items-center justify-center h-10 px-5 rounded-md bg-blue-700 text-white hover:bg-blue-800 transition-colors"
                      >
                        Læs hvorfor FlyFiles findes
                      </Link>
                      <Link
                        href="/pricing"
                        className="inline-flex items-center justify-center h-10 px-5 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
                      >
                        Se hvordan vi holder prisen nede
                      </Link>
                    </div>
                  </div>

                  <div className="mt-6 flex flex-wrap gap-3">
                    <Link
                      href="/pricing"
                      className="inline-flex items-center justify-center h-10 px-5 rounded-md bg-blue-700 text-white hover:bg-blue-800 transition-colors"
                    >
                      Se priser
                    </Link>
                    <Link
                      href="/om-os/hvorfor-flyfiles-findes"
                      className="inline-flex items-center justify-center h-10 px-5 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
                    >
                      Hvorfor FlyFiles findes
                    </Link>
                  </div>
                </div>

                {/* CTA inside content column so sidebar stays sticky across it */}
                <div className="mt-12">
                  <div className="rounded-2xl border border-blue-100 shadow-sm p-6 md:p-10 bg-white">
                    <div className="grid md:grid-cols-2 gap-6 items-center">
                      <div>
                        <h3 className="text-2xl md:text-3xl font-extrabold text-gray-900">
                          Klar til at prøve FlyFiles?
                        </h3>
                        <p className="mt-2 text-gray-700">
                          Start enkelt og uden bøvl – del store filer hurtigt og sikkert. Dansk løsning, dansk support.
                        </p>
                      </div>
                      <div className="flex flex-wrap gap-3 md:justify-end justify-center">
                        <Link
                          href="/"
                          className="inline-flex items-center justify-center h-10 px-6 rounded-md bg-blue-700 text-white hover:bg-blue-800 transition-colors"
                        >
                          Upload som gæst
                        </Link>
                        <Link
                          href="/login"
                          className="inline-flex items-center justify-center h-10 px-6 rounded-md border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors"
                        >
                          Log ind
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}